"""
Code reasoning benchmarks: HumanEval, MBPP
Based on the evaluation used in the Absolute Zero paper.
"""

import json
import re
import sys
import os
from typing import List, Dict, Any, Tuple
from datasets import load_dataset
import subprocess
import tempfile
import signal
from contextlib import contextmanager

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from src.python_executor import PythonExecutor


class CodeBenchmark:
    """Base class for code benchmarks."""
    
    def __init__(self, timeout: int = 10):
        self.timeout = timeout
        self.executor = PythonExecutor(timeout=timeout)
    
    def evaluate_solution(self, problem: str, solution: str, test_cases: List[str]) -> Dict[str, Any]:
        """Evaluate a solution against test cases."""
        results = {
            'passed': 0,
            'total': len(test_cases),
            'success_rate': 0.0,
            'execution_results': [],
            'errors': []
        }
        
        for i, test_case in enumerate(test_cases):
            try:
                # Combine solution with test case
                full_code = f"{solution}\n\n{test_case}"
                
                # Execute the code
                exec_result = self.executor.execute_code(full_code)
                
                if exec_result['success'] and not exec_result['error']:
                    results['passed'] += 1
                    results['execution_results'].append({
                        'test_case': i,
                        'status': 'passed',
                        'output': exec_result['output']
                    })
                else:
                    results['execution_results'].append({
                        'test_case': i,
                        'status': 'failed',
                        'error': exec_result['error'],
                        'output': exec_result['output']
                    })
                    results['errors'].append(f"Test {i}: {exec_result['error']}")
                    
            except Exception as e:
                results['execution_results'].append({
                    'test_case': i,
                    'status': 'error',
                    'error': str(e)
                })
                results['errors'].append(f"Test {i}: {str(e)}")
        
        results['success_rate'] = results['passed'] / results['total'] if results['total'] > 0 else 0.0
        return results


class HumanEvalBenchmark(CodeBenchmark):
    """HumanEval benchmark for code generation."""
    
    def __init__(self, timeout: int = 10):
        super().__init__(timeout)
        self.dataset = None
        self.problems = []
        
    def load_dataset(self, num_problems: int = 50):
        """Load HumanEval dataset."""
        try:
            # Load from HuggingFace datasets
            self.dataset = load_dataset("openai_humaneval", split="test")
            
            # Take first num_problems for evaluation
            self.problems = list(self.dataset.select(range(min(num_problems, len(self.dataset)))))
            
            print(f"Loaded {len(self.problems)} HumanEval problems")
            return True
            
        except Exception as e:
            print(f"Failed to load HumanEval dataset: {e}")
            # Create some sample problems for testing
            self.problems = self._create_sample_problems()
            print(f"Using {len(self.problems)} sample problems")
            return False
    
    def _create_sample_problems(self) -> List[Dict[str, Any]]:
        """Create sample problems for testing when dataset is not available."""
        return [
            {
                "task_id": "HumanEval/0",
                "prompt": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n",
                "canonical_solution": "    for idx, elem in enumerate(numbers):\n        for idx2, elem2 in enumerate(numbers):\n            if idx != idx2:\n                distance = abs(elem - elem2)\n                if distance < threshold:\n                    return True\n\n    return False\n",
                "test": "def check(candidate):\n    assert candidate([1.0, 2.0, 3.0], 0.5) == False\n    assert candidate([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3) == True\n    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3) == True\n    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05) == False\n    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.95) == True\n    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.8) == False\n    assert candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1) == True\n\ncheck(has_close_elements)"
            },
            {
                "task_id": "HumanEval/1", 
                "prompt": "def separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    separate those group into separate strings and return the list of those.\n    Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    Ignore any spaces in the input string.\n    >>> separate_paren_groups('( ) (( )) (( )( ))')\n    ['()', '(())', '(()())']\n    \"\"\"\n",
                "canonical_solution": "    result = []\n    current_string = []\n    current_depth = 0\n\n    for c in paren_string:\n        if c == '(':\n            current_depth += 1\n            current_string.append(c)\n        elif c == ')':\n            current_depth -= 1\n            current_string.append(c)\n\n            if current_depth == 0:\n                result.append(''.join(current_string))\n                current_string = []\n\n    return result\n",
                "test": "def check(candidate):\n    assert candidate('(()()) ((())) () ((())()())') == ['(()())', '((()))', '()', '((())()())']\n    assert candidate('() (()) ((())) (((())))') == ['()', '(())', '((()))', '(((())))']\n    assert candidate('(()(())((())))') == ['(()(())((())))'] \n    assert candidate('( ) (( )) (( )( ))') == ['()', '(())', '(()())']\n\ncheck(separate_paren_groups)"
            },
            {
                "task_id": "HumanEval/2",
                "prompt": "def truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    and integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \"\"\"\n",
                "canonical_solution": "    return number % 1.0\n",
                "test": "def check(candidate):\n    assert candidate(3.5) == 0.5\n    assert abs(candidate(1.33) - 0.33) < 1e-6\n    assert abs(candidate(123.456) - 0.456) < 1e-6\n\ncheck(truncate_number)"
            }
        ]
    
    def evaluate_model(self, model, num_problems: int = None) -> Dict[str, Any]:
        """Evaluate a model on HumanEval problems."""
        if not self.problems:
            self.load_dataset()
        
        if num_problems is None:
            num_problems = len(self.problems)
        
        results = {
            'total_problems': min(num_problems, len(self.problems)),
            'solved': 0,
            'pass_at_1': 0.0,
            'problem_results': [],
            'errors': []
        }
        
        for i, problem in enumerate(self.problems[:num_problems]):
            print(f"Evaluating problem {i+1}/{num_problems}: {problem['task_id']}")
            
            try:
                # Generate solution using the model
                prompt = f"Complete this Python function:\n\n{problem['prompt']}"
                generated_solution = model.generate(prompt, max_length=512, temperature=0.1)
                
                # Extract the function implementation
                full_solution = problem['prompt'] + generated_solution
                
                # Create test case
                test_code = problem['test']
                
                # Evaluate the solution
                eval_result = self.evaluate_solution(
                    problem['prompt'], 
                    full_solution, 
                    [test_code]
                )
                
                problem_result = {
                    'task_id': problem['task_id'],
                    'prompt': problem['prompt'][:100] + "...",
                    'generated_solution': generated_solution[:200] + "...",
                    'passed': eval_result['passed'] > 0,
                    'success_rate': eval_result['success_rate'],
                    'errors': eval_result['errors']
                }
                
                if eval_result['passed'] > 0:
                    results['solved'] += 1
                
                results['problem_results'].append(problem_result)
                
            except Exception as e:
                error_msg = f"Problem {problem['task_id']}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"Error: {error_msg}")
        
        results['pass_at_1'] = results['solved'] / results['total_problems'] if results['total_problems'] > 0 else 0.0
        
        return results


class MBPPBenchmark(CodeBenchmark):
    """MBPP (Mostly Basic Python Problems) benchmark."""
    
    def __init__(self, timeout: int = 10):
        super().__init__(timeout)
        self.problems = []
    
    def load_dataset(self, num_problems: int = 50):
        """Load MBPP dataset."""
        try:
            # Load from HuggingFace datasets
            dataset = load_dataset("mbpp", split="test")
            self.problems = list(dataset.select(range(min(num_problems, len(dataset)))))
            print(f"Loaded {len(self.problems)} MBPP problems")
            return True
            
        except Exception as e:
            print(f"Failed to load MBPP dataset: {e}")
            # Create sample problems
            self.problems = self._create_sample_problems()
            print(f"Using {len(self.problems)} sample problems")
            return False
    
    def _create_sample_problems(self) -> List[Dict[str, Any]]:
        """Create sample MBPP-style problems."""
        return [
            {
                "task_id": 1,
                "text": "Write a function to find the minimum cost path to reach (m, n) from (0, 0) for the given cost matrix cost[][] and a position (m, n) in cost[][].",
                "code": "def min_cost(cost, m, n): \n\tR = 3\n\tC = 3\n\ttc = [[0 for x in range(C)] for x in range(R)] \n\ttc[0][0] = cost[0][0] \n\tfor i in range(1, m + 1): \n\t\ttc[i][0] = tc[i - 1][0] + cost[i][0] \n\tfor j in range(1, n + 1): \n\t\ttc[0][j] = tc[0][j - 1] + cost[0][j] \n\tfor i in range(1, m + 1): \n\t\tfor j in range(1, n + 1): \n\t\t\ttc[i][j] = min(tc[i - 1][j - 1], tc[i - 1][j], tc[i][j - 1]) + cost[i][j] \n\treturn tc[m][n]",
                "test_list": ["assert min_cost([[1, 2, 3], [4, 8, 2], [1, 5, 3]], 2, 2) == 8", "assert min_cost([[2, 3, 4], [5, 9, 3], [2, 6, 4]], 2, 2) == 12", "assert min_cost([[3, 4, 5], [6, 10, 4], [3, 7, 5]], 2, 2) == 16"]
            },
            {
                "task_id": 2,
                "text": "Write a function to find the similar elements from the given two tuple lists.",
                "code": "def similar_elements(test_tup1, test_tup2):\n  res = tuple(set(test_tup1) & set(test_tup2))\n  return (res) ",
                "test_list": ["assert similar_elements((3, 4, 5, 6),(5, 7, 4, 10)) == (4, 5)", "assert similar_elements((1, 2, 3, 4),(5, 4, 3, 7)) == (3, 4)", "assert similar_elements((11, 12, 14, 13),(17, 15, 14, 13)) == (13, 14)"]
            }
        ]
    
    def evaluate_model(self, model, num_problems: int = None) -> Dict[str, Any]:
        """Evaluate a model on MBPP problems."""
        if not self.problems:
            self.load_dataset()
        
        if num_problems is None:
            num_problems = len(self.problems)
        
        results = {
            'total_problems': min(num_problems, len(self.problems)),
            'solved': 0,
            'pass_at_1': 0.0,
            'problem_results': [],
            'errors': []
        }
        
        for i, problem in enumerate(self.problems[:num_problems]):
            print(f"Evaluating MBPP problem {i+1}/{num_problems}: {problem['task_id']}")
            
            try:
                # Generate solution
                prompt = f"Write a Python function to solve this problem:\n\n{problem['text']}\n\nFunction:"
                generated_solution = model.generate(prompt, max_length=512, temperature=0.1)
                
                # Evaluate against test cases
                eval_result = self.evaluate_solution(
                    problem['text'],
                    generated_solution,
                    problem['test_list']
                )
                
                problem_result = {
                    'task_id': problem['task_id'],
                    'text': problem['text'][:100] + "...",
                    'generated_solution': generated_solution[:200] + "...",
                    'passed': eval_result['passed'],
                    'total_tests': eval_result['total'],
                    'success_rate': eval_result['success_rate'],
                    'errors': eval_result['errors']
                }
                
                if eval_result['success_rate'] >= 1.0:  # All tests passed
                    results['solved'] += 1
                
                results['problem_results'].append(problem_result)
                
            except Exception as e:
                error_msg = f"Problem {problem['task_id']}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"Error: {error_msg}")
        
        results['pass_at_1'] = results['solved'] / results['total_problems'] if results['total_problems'] > 0 else 0.0
        
        return results


# Example usage and testing
if __name__ == "__main__":
    # Test with a mock model
    class MockModel:
        def generate(self, prompt, **kwargs):
            if "has_close_elements" in prompt:
                return """
    for i in range(len(numbers)):
        for j in range(i+1, len(numbers)):
            if abs(numbers[i] - numbers[j]) < threshold:
                return True
    return False
"""
            elif "separate_paren_groups" in prompt:
                return """
    result = []
    current = ""
    depth = 0
    
    for char in paren_string:
        if char == '(':
            current += char
            depth += 1
        elif char == ')':
            current += char
            depth -= 1
            if depth == 0:
                result.append(current)
                current = ""
    
    return result
"""
            else:
                return "def solution(): pass"
    
    # Test HumanEval
    print("Testing HumanEval benchmark...")
    humaneval = HumanEvalBenchmark()
    humaneval.load_dataset(3)
    
    mock_model = MockModel()
    results = humaneval.evaluate_model(mock_model, 2)
    
    print(f"HumanEval Results:")
    print(f"  Pass@1: {results['pass_at_1']:.2%}")
    print(f"  Solved: {results['solved']}/{results['total_problems']}")
    
    # Test MBPP
    print("\nTesting MBPP benchmark...")
    mbpp = MBPPBenchmark()
    mbpp.load_dataset(2)
    
    results = mbpp.evaluate_model(mock_model, 2)
    
    print(f"MBPP Results:")
    print(f"  Pass@1: {results['pass_at_1']:.2%}")
    print(f"  Solved: {results['solved']}/{results['total_problems']}")
