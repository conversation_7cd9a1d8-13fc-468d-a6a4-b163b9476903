"""
Comprehensive evaluation framework for Absolute Zero Reasoner.
Compares baseline vs trained models on standard benchmarks.
"""

import json
import os
import sys
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
import matplotlib.pyplot as plt
import pandas as pd

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from src.model_interface import create_model_interface
from src.trainer import AbsoluteZeroTrainer
from src.utils import load_config, save_json

from .code_benchmarks import HumanEvalBenchmark, MBPPBenchmark
from .math_benchmarks import GSM8KBenchmark, MATHBenchmark


class AbsoluteZeroEvaluator:
    """Comprehensive evaluator for Absolute Zero Reasoner."""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = load_config(config_path)
        self.results_dir = "evaluation_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Initialize benchmarks
        self.benchmarks = {
            'humaneval': HumanEvalBenchmark(),
            'mbpp': MBPPBenchmark(),
            'gsm8k': GSM8KBenchmark(),
            'math': MATHBenchmark()
        }
        
        # Load datasets
        print("Loading benchmark datasets...")
        for name, benchmark in self.benchmarks.items():
            print(f"Loading {name}...")
            benchmark.load_dataset(50 if name in ['humaneval', 'mbpp'] else 100)
    
    def evaluate_baseline(self, model_name: str = None, num_problems: Dict[str, int] = None) -> Dict[str, Any]:
        """Evaluate baseline model performance."""
        if model_name is None:
            model_name = self.config['model']['name']
        
        if num_problems is None:
            num_problems = {
                'humaneval': 20,
                'mbpp': 20, 
                'gsm8k': 50,
                'math': 30
            }
        
        print(f"Evaluating baseline model: {model_name}")
        
        # Load baseline model
        baseline_model = create_model_interface(model_name)
        
        baseline_results = {
            'model_name': model_name,
            'evaluation_type': 'baseline',
            'timestamp': datetime.now().isoformat(),
            'benchmarks': {}
        }
        
        # Evaluate on each benchmark
        for bench_name, benchmark in self.benchmarks.items():
            if bench_name in num_problems:
                print(f"\nEvaluating {bench_name} ({num_problems[bench_name]} problems)...")
                
                start_time = time.time()
                results = benchmark.evaluate_model(baseline_model, num_problems[bench_name])
                eval_time = time.time() - start_time
                
                results['evaluation_time'] = eval_time
                baseline_results['benchmarks'][bench_name] = results
                
                # Print summary
                if bench_name in ['humaneval', 'mbpp']:
                    print(f"  {bench_name.upper()} Pass@1: {results['pass_at_1']:.2%}")
                else:
                    print(f"  {bench_name.upper()} Accuracy: {results['accuracy']:.2%}")
        
        # Save results
        baseline_path = os.path.join(self.results_dir, f"baseline_{model_name.replace('/', '_')}.json")
        save_json(baseline_results, baseline_path)
        print(f"\nBaseline results saved to: {baseline_path}")
        
        return baseline_results
    
    def train_and_evaluate(self, training_iterations: int = 50, 
                          eval_intervals: List[int] = None) -> Dict[str, Any]:
        """Train model with Absolute Zero and evaluate at intervals."""
        
        if eval_intervals is None:
            eval_intervals = [10, 25, 50]
        
        print(f"Training Absolute Zero model for {training_iterations} iterations...")
        
        # Initialize trainer
        trainer = AbsoluteZeroTrainer(self.config)
        
        training_results = {
            'model_name': self.config['model']['name'],
            'evaluation_type': 'absolute_zero_training',
            'timestamp': datetime.now().isoformat(),
            'training_iterations': training_iterations,
            'eval_intervals': eval_intervals,
            'evaluations': {}
        }
        
        # Evaluate before training (baseline)
        print("\nEvaluating before training...")
        pre_training_results = self._evaluate_current_model(trainer.model, "pre_training")
        training_results['evaluations']['pre_training'] = pre_training_results
        
        # Training loop with periodic evaluation
        for iteration in range(training_iterations):
            print(f"\nTraining iteration {iteration + 1}/{training_iterations}")
            
            # Run one training iteration
            if iteration % 2 == 0:
                # PROPOSE phase
                iteration_results = trainer._propose_phase(self.config['training']['batch_size'])
            else:
                # SOLVE phase  
                iteration_results = trainer._solve_phase(self.config['training']['batch_size'])
            
            # Update model (simplified)
            if iteration_results['rewards']:
                trainer._update_model(iteration_results['rewards'])
            
            # Evaluate at specified intervals
            if (iteration + 1) in eval_intervals:
                print(f"\nEvaluating at iteration {iteration + 1}...")
                eval_results = self._evaluate_current_model(trainer.model, f"iteration_{iteration + 1}")
                training_results['evaluations'][f'iteration_{iteration + 1}'] = eval_results
        
        # Final evaluation
        print("\nFinal evaluation...")
        final_results = self._evaluate_current_model(trainer.model, "final")
        training_results['evaluations']['final'] = final_results
        
        # Save training results
        training_path = os.path.join(self.results_dir, f"training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        save_json(training_results, training_path)
        print(f"\nTraining results saved to: {training_path}")
        
        return training_results
    
    def _evaluate_current_model(self, model, eval_name: str) -> Dict[str, Any]:
        """Evaluate current model state."""
        
        # Reduced problem counts for faster evaluation during training
        num_problems = {
            'humaneval': 10,
            'mbpp': 10,
            'gsm8k': 20, 
            'math': 15
        }
        
        eval_results = {
            'eval_name': eval_name,
            'timestamp': datetime.now().isoformat(),
            'benchmarks': {}
        }
        
        for bench_name, benchmark in self.benchmarks.items():
            print(f"  Evaluating {bench_name}...")
            
            start_time = time.time()
            results = benchmark.evaluate_model(model, num_problems[bench_name])
            eval_time = time.time() - start_time
            
            results['evaluation_time'] = eval_time
            eval_results['benchmarks'][bench_name] = results
            
            # Print summary
            if bench_name in ['humaneval', 'mbpp']:
                print(f"    {bench_name.upper()} Pass@1: {results['pass_at_1']:.2%}")
            else:
                print(f"    {bench_name.upper()} Accuracy: {results['accuracy']:.2%}")
        
        return eval_results
    
    def compare_results(self, baseline_path: str, training_path: str) -> Dict[str, Any]:
        """Compare baseline vs trained model results."""
        
        # Load results
        with open(baseline_path, 'r') as f:
            baseline_results = json.load(f)
        
        with open(training_path, 'r') as f:
            training_results = json.load(f)
        
        # Extract final training results
        final_training = training_results['evaluations']['final']
        
        comparison = {
            'baseline_model': baseline_results['model_name'],
            'comparison_timestamp': datetime.now().isoformat(),
            'improvements': {},
            'summary': {}
        }
        
        print("\n" + "="*60)
        print("BASELINE vs ABSOLUTE ZERO COMPARISON")
        print("="*60)
        
        total_improvements = []
        
        for bench_name in ['humaneval', 'mbpp', 'gsm8k', 'math']:
            if bench_name in baseline_results['benchmarks'] and bench_name in final_training['benchmarks']:
                
                baseline_score = baseline_results['benchmarks'][bench_name]
                trained_score = final_training['benchmarks'][bench_name]
                
                if bench_name in ['humaneval', 'mbpp']:
                    baseline_metric = baseline_score['pass_at_1']
                    trained_metric = trained_score['pass_at_1']
                    metric_name = 'Pass@1'
                else:
                    baseline_metric = baseline_score['accuracy']
                    trained_metric = trained_score['accuracy']
                    metric_name = 'Accuracy'
                
                improvement = trained_metric - baseline_metric
                improvement_pct = (improvement / baseline_metric * 100) if baseline_metric > 0 else 0
                
                comparison['improvements'][bench_name] = {
                    'baseline': baseline_metric,
                    'trained': trained_metric,
                    'improvement': improvement,
                    'improvement_pct': improvement_pct,
                    'metric_name': metric_name
                }
                
                total_improvements.append(improvement)
                
                print(f"\n{bench_name.upper()}:")
                print(f"  Baseline {metric_name}: {baseline_metric:.2%}")
                print(f"  Trained {metric_name}: {trained_metric:.2%}")
                print(f"  Improvement: {improvement:+.2%} ({improvement_pct:+.1f}%)")
        
        # Overall summary
        avg_improvement = sum(total_improvements) / len(total_improvements) if total_improvements else 0
        comparison['summary'] = {
            'average_improvement': avg_improvement,
            'total_benchmarks': len(total_improvements),
            'positive_improvements': sum(1 for imp in total_improvements if imp > 0)
        }
        
        print(f"\n{'='*60}")
        print("SUMMARY:")
        print(f"  Average Improvement: {avg_improvement:+.2%}")
        print(f"  Benchmarks Improved: {comparison['summary']['positive_improvements']}/{comparison['summary']['total_benchmarks']}")
        print(f"{'='*60}")
        
        # Save comparison
        comparison_path = os.path.join(self.results_dir, f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        save_json(comparison, comparison_path)
        print(f"\nComparison saved to: {comparison_path}")
        
        return comparison
    
    def plot_training_progress(self, training_results: Dict[str, Any], save_path: str = None):
        """Plot training progress over time."""
        
        evaluations = training_results['evaluations']
        
        # Extract data for plotting
        eval_points = []
        metrics = {
            'humaneval': [],
            'mbpp': [],
            'gsm8k': [],
            'math': []
        }
        
        for eval_name, eval_data in evaluations.items():
            if eval_name == 'pre_training':
                eval_points.append(0)
            elif eval_name.startswith('iteration_'):
                iteration_num = int(eval_name.split('_')[1])
                eval_points.append(iteration_num)
            elif eval_name == 'final':
                eval_points.append(training_results['training_iterations'])
            else:
                continue
            
            # Extract metrics
            for bench_name in metrics.keys():
                if bench_name in eval_data['benchmarks']:
                    bench_data = eval_data['benchmarks'][bench_name]
                    if bench_name in ['humaneval', 'mbpp']:
                        metrics[bench_name].append(bench_data['pass_at_1'])
                    else:
                        metrics[bench_name].append(bench_data['accuracy'])
                else:
                    metrics[bench_name].append(0)
        
        # Create plot
        plt.figure(figsize=(12, 8))
        
        for bench_name, values in metrics.items():
            if len(values) == len(eval_points):
                plt.plot(eval_points, [v * 100 for v in values], 
                        marker='o', label=bench_name.upper(), linewidth=2)
        
        plt.xlabel('Training Iteration')
        plt.ylabel('Performance (%)')
        plt.title('Absolute Zero Training Progress')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Training progress plot saved to: {save_path}")
        else:
            plt.show()
    
    def run_full_evaluation(self, training_iterations: int = 50) -> Dict[str, Any]:
        """Run complete evaluation: baseline + training + comparison."""
        
        print("🚀 Starting Full Absolute Zero Evaluation")
        print("="*60)
        
        # Step 1: Evaluate baseline
        print("\n📊 Step 1: Evaluating Baseline Model")
        baseline_results = self.evaluate_baseline()
        
        # Step 2: Train and evaluate
        print("\n🎯 Step 2: Training with Absolute Zero")
        training_results = self.train_and_evaluate(training_iterations)
        
        # Step 3: Compare results
        print("\n📈 Step 3: Comparing Results")
        baseline_path = os.path.join(self.results_dir, f"baseline_{self.config['model']['name'].replace('/', '_')}.json")
        training_path = max([f for f in os.listdir(self.results_dir) if f.startswith('training_results_')], 
                           key=lambda x: os.path.getctime(os.path.join(self.results_dir, x)))
        training_path = os.path.join(self.results_dir, training_path)
        
        comparison = self.compare_results(baseline_path, training_path)
        
        # Step 4: Generate plots
        print("\n📊 Step 4: Generating Visualizations")
        plot_path = os.path.join(self.results_dir, f"training_progress_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        self.plot_training_progress(training_results, plot_path)
        
        return {
            'baseline_results': baseline_results,
            'training_results': training_results,
            'comparison': comparison,
            'plot_path': plot_path
        }


# Example usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Evaluate Absolute Zero Reasoner')
    parser.add_argument('--baseline-only', action='store_true', help='Only evaluate baseline')
    parser.add_argument('--training-iterations', type=int, default=20, help='Number of training iterations')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='Config file path')
    
    args = parser.parse_args()
    
    evaluator = AbsoluteZeroEvaluator(args.config)
    
    if args.baseline_only:
        evaluator.evaluate_baseline()
    else:
        evaluator.run_full_evaluation(args.training_iterations)
