"""
Math reasoning benchmarks: GSM8K, MATH
Based on the evaluation used in the Absolute Zero paper.
"""

import json
import re
import sys
import os
from typing import List, Dict, Any, Tuple, Optional
from datasets import load_dataset
import math

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from src.python_executor import PythonExecutor


class MathBenchmark:
    """Base class for math benchmarks."""
    
    def __init__(self):
        self.executor = PythonExecutor(timeout=10)
    
    def extract_answer(self, text: str) -> Optional[str]:
        """Extract the final answer from generated text."""
        # Look for common answer patterns
        patterns = [
            r'(?:the answer is|answer:|final answer:|therefore,?)\s*([+-]?\d+(?:\.\d+)?)',
            r'(?:equals?|=)\s*([+-]?\d+(?:\.\d+)?)',
            r'([+-]?\d+(?:\.\d+)?)\s*(?:is the answer|is the final answer)',
            r'\$([+-]?\d+(?:\.\d+)?)\$',  # LaTeX format
            r'\\boxed\{([^}]+)\}',  # LaTeX boxed format
        ]
        
        text = text.lower().strip()
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return matches[-1].strip()  # Return the last match
        
        # If no pattern matches, try to find the last number in the text
        numbers = re.findall(r'([+-]?\d+(?:\.\d+)?)', text)
        if numbers:
            return numbers[-1]
        
        return None
    
    def normalize_answer(self, answer: str) -> Optional[float]:
        """Normalize answer to a comparable format."""
        if not answer:
            return None
        
        try:
            # Remove common formatting
            answer = answer.replace(',', '').replace('$', '').strip()
            
            # Handle fractions
            if '/' in answer:
                parts = answer.split('/')
                if len(parts) == 2:
                    return float(parts[0]) / float(parts[1])
            
            # Handle percentages
            if '%' in answer:
                return float(answer.replace('%', '')) / 100
            
            return float(answer)
            
        except (ValueError, ZeroDivisionError):
            return None
    
    def check_answer_match(self, predicted: str, expected: str, tolerance: float = 1e-6) -> bool:
        """Check if predicted answer matches expected answer."""
        pred_num = self.normalize_answer(predicted)
        exp_num = self.normalize_answer(expected)
        
        if pred_num is None or exp_num is None:
            # Fallback to string comparison
            return predicted.strip().lower() == expected.strip().lower()
        
        return abs(pred_num - exp_num) < tolerance


class GSM8KBenchmark(MathBenchmark):
    """GSM8K (Grade School Math 8K) benchmark."""
    
    def __init__(self):
        super().__init__()
        self.dataset = None
        self.problems = []
    
    def load_dataset(self, num_problems: int = 100):
        """Load GSM8K dataset."""
        try:
            # Load from HuggingFace datasets
            self.dataset = load_dataset("gsm8k", "main", split="test")
            self.problems = list(self.dataset.select(range(min(num_problems, len(self.dataset)))))
            print(f"Loaded {len(self.problems)} GSM8K problems")
            return True
            
        except Exception as e:
            print(f"Failed to load GSM8K dataset: {e}")
            # Create sample problems
            self.problems = self._create_sample_problems()
            print(f"Using {len(self.problems)} sample problems")
            return False
    
    def _create_sample_problems(self) -> List[Dict[str, Any]]:
        """Create sample GSM8K-style problems."""
        return [
            {
                "question": "Natalia sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did Natalia sell altogether in April and May?",
                "answer": "Natalia sold 48/2 = 24 clips in May.\nNatalia sold 48+24 = 72 clips altogether in April and May.\n#### 72"
            },
            {
                "question": "Weng earns $12 an hour for babysitting. Yesterday, she just did 50 minutes of babysitting. How much did she earn?",
                "answer": "Weng earns 12/60 = $0.2 per minute.\nWorking 50 minutes, she earned 0.2 x 50 = $10.\n#### 10"
            },
            {
                "question": "Betty is saving money for a new wallet which costs $100. Betty has only half of the money she needs. Her parents decided to give her $15 for that purpose, and her grandparents twice as much as her parents. How much more money does Betty need to buy the wallet?",
                "answer": "In the beginning, Betty has only 100/2 = $50.\nBetty's grandparents gave her 15 * 2 = $30.\nThis means, Betty needs 100 - 50 - 15 - 30 = $5 more.\n#### 5"
            },
            {
                "question": "Julie is reading a 120-page book. Yesterday, she was able to read 12 pages and today, she read twice as many pages as yesterday. If she wants to read half of the remaining pages tomorrow, how many pages should she read?",
                "answer": "Maila read 12 x 2 = 24 pages today.\nSo she was able to read a total of 12 + 24 = 36 pages since yesterday.\nThere are 120 - 36 = 84 pages left to be read.\nSince she wants to read half of the remaining pages tomorrow, then she should read 84/2 = 42 pages.\n#### 42"
            },
            {
                "question": "James writes a 3-page letter to 2 different friends. He uses both sides of the page and writes 250 words per side. How many words did he write total?",
                "answer": "He wrote each friend 3*2=6 sides\nSo he wrote 6*2=12 sides total\nThat means he wrote 12*250=3000 words\n#### 3000"
            }
        ]
    
    def evaluate_model(self, model, num_problems: int = None) -> Dict[str, Any]:
        """Evaluate a model on GSM8K problems."""
        if not self.problems:
            self.load_dataset()
        
        if num_problems is None:
            num_problems = len(self.problems)
        
        results = {
            'total_problems': min(num_problems, len(self.problems)),
            'correct': 0,
            'accuracy': 0.0,
            'problem_results': [],
            'errors': []
        }
        
        for i, problem in enumerate(self.problems[:num_problems]):
            print(f"Evaluating GSM8K problem {i+1}/{num_problems}")
            
            try:
                # Create prompt
                prompt = f"""Solve this math problem step by step:

{problem['question']}

Let me work through this step by step:"""
                
                # Generate solution
                generated_solution = model.generate(prompt, max_length=512, temperature=0.1)
                
                # Extract answers
                predicted_answer = self.extract_answer(generated_solution)
                expected_answer = self.extract_answer(problem['answer'])
                
                # Check if correct
                is_correct = False
                if predicted_answer and expected_answer:
                    is_correct = self.check_answer_match(predicted_answer, expected_answer)
                
                if is_correct:
                    results['correct'] += 1
                
                problem_result = {
                    'question': problem['question'][:100] + "...",
                    'generated_solution': generated_solution[:300] + "...",
                    'predicted_answer': predicted_answer,
                    'expected_answer': expected_answer,
                    'correct': is_correct
                }
                
                results['problem_results'].append(problem_result)
                
            except Exception as e:
                error_msg = f"Problem {i}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"Error: {error_msg}")
        
        results['accuracy'] = results['correct'] / results['total_problems'] if results['total_problems'] > 0 else 0.0
        
        return results


class MATHBenchmark(MathBenchmark):
    """MATH benchmark for competition-level mathematics."""
    
    def __init__(self):
        super().__init__()
        self.problems = []
    
    def load_dataset(self, num_problems: int = 50):
        """Load MATH dataset."""
        try:
            # Load from HuggingFace datasets
            dataset = load_dataset("competition_math", split="test")
            self.problems = list(dataset.select(range(min(num_problems, len(dataset)))))
            print(f"Loaded {len(self.problems)} MATH problems")
            return True
            
        except Exception as e:
            print(f"Failed to load MATH dataset: {e}")
            # Create sample problems
            self.problems = self._create_sample_problems()
            print(f"Using {len(self.problems)} sample problems")
            return False
    
    def _create_sample_problems(self) -> List[Dict[str, Any]]:
        """Create sample MATH-style problems."""
        return [
            {
                "problem": "Find the domain of the expression $\\frac{\\sqrt{x-2}}{\\sqrt{5-x}}$.",
                "solution": "The expressions inside each square root must be non-negative. Therefore, $x-2 \\ge 0$, so $x \\ge 2$, and $5 - x \\ge 0$, so $x \\le 5$. Also, the denominator cannot be equal to zero, so $5-x > 0$, which gives us $x < 5$. Therefore, the domain of the expression is $[2,5)$.",
                "answer": "[2,5)",
                "subject": "Algebra",
                "level": "Level 5"
            },
            {
                "problem": "If $\\det \\mathbf{A} = 2$ and $\\det \\mathbf{B} = 12$, then find $\\det (\\mathbf{A} \\mathbf{B})$.",
                "solution": "We have that $\\det (\\mathbf{A} \\mathbf{B}) = \\det \\mathbf{A} \\cdot \\det \\mathbf{B} = 2 \\cdot 12 = 24$.",
                "answer": "24",
                "subject": "Precalculus",
                "level": "Level 1"
            },
            {
                "problem": "Compute $\\dbinom{10}{5}$.",
                "solution": "$\\dbinom{10}{5} = \\dfrac{10!}{5!(10-5)!} = \\dfrac{10!}{5! \\cdot 5!} = \\dfrac{10 \\cdot 9 \\cdot 8 \\cdot 7 \\cdot 6}{5 \\cdot 4 \\cdot 3 \\cdot 2 \\cdot 1} = \\dfrac{30240}{120} = 252$.",
                "answer": "252",
                "subject": "Counting & Probability",
                "level": "Level 1"
            }
        ]
    
    def evaluate_model(self, model, num_problems: int = None) -> Dict[str, Any]:
        """Evaluate a model on MATH problems."""
        if not self.problems:
            self.load_dataset()
        
        if num_problems is None:
            num_problems = len(self.problems)
        
        results = {
            'total_problems': min(num_problems, len(self.problems)),
            'correct': 0,
            'accuracy': 0.0,
            'problem_results': [],
            'errors': [],
            'by_subject': {},
            'by_level': {}
        }
        
        for i, problem in enumerate(self.problems[:num_problems]):
            print(f"Evaluating MATH problem {i+1}/{num_problems}")
            
            try:
                # Create prompt
                prompt = f"""Solve this mathematics problem:

{problem['problem']}

Solution:"""
                
                # Generate solution
                generated_solution = model.generate(prompt, max_length=512, temperature=0.1)
                
                # Extract answers
                predicted_answer = self.extract_answer(generated_solution)
                expected_answer = problem['answer']
                
                # Check if correct
                is_correct = False
                if predicted_answer and expected_answer:
                    is_correct = self.check_answer_match(predicted_answer, expected_answer)
                
                if is_correct:
                    results['correct'] += 1
                
                # Track by subject and level
                subject = problem.get('subject', 'Unknown')
                level = problem.get('level', 'Unknown')
                
                if subject not in results['by_subject']:
                    results['by_subject'][subject] = {'correct': 0, 'total': 0}
                if level not in results['by_level']:
                    results['by_level'][level] = {'correct': 0, 'total': 0}
                
                results['by_subject'][subject]['total'] += 1
                results['by_level'][level]['total'] += 1
                
                if is_correct:
                    results['by_subject'][subject]['correct'] += 1
                    results['by_level'][level]['correct'] += 1
                
                problem_result = {
                    'problem': problem['problem'][:100] + "...",
                    'generated_solution': generated_solution[:300] + "...",
                    'predicted_answer': predicted_answer,
                    'expected_answer': expected_answer,
                    'correct': is_correct,
                    'subject': subject,
                    'level': level
                }
                
                results['problem_results'].append(problem_result)
                
            except Exception as e:
                error_msg = f"Problem {i}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"Error: {error_msg}")
        
        results['accuracy'] = results['correct'] / results['total_problems'] if results['total_problems'] > 0 else 0.0
        
        # Calculate accuracy by subject and level
        for subject_data in results['by_subject'].values():
            subject_data['accuracy'] = subject_data['correct'] / subject_data['total'] if subject_data['total'] > 0 else 0.0
        
        for level_data in results['by_level'].values():
            level_data['accuracy'] = level_data['correct'] / level_data['total'] if level_data['total'] > 0 else 0.0
        
        return results


# Example usage and testing
if __name__ == "__main__":
    # Test with a mock model
    class MockModel:
        def generate(self, prompt, **kwargs):
            if "natalia sold clips" in prompt.lower():
                return "Natalia sold 48 clips in April. In May she sold half as many, which is 48/2 = 24 clips. In total she sold 48 + 24 = 72 clips."
            elif "weng earns" in prompt.lower():
                return "Weng earns $12 per hour. 50 minutes is 50/60 hours. So she earned 12 * (50/60) = 12 * 0.833 = $10."
            elif "domain of the expression" in prompt.lower():
                return "For the square root in numerator, we need x-2 >= 0, so x >= 2. For the square root in denominator, we need 5-x > 0 (not equal to zero), so x < 5. Therefore the domain is [2,5)."
            elif "det" in prompt.lower():
                return "Using the property that det(AB) = det(A) * det(B), we get det(AB) = 2 * 12 = 24."
            else:
                return "Let me solve this step by step. The answer is 42."
    
    # Test GSM8K
    print("Testing GSM8K benchmark...")
    gsm8k = GSM8KBenchmark()
    gsm8k.load_dataset(3)
    
    mock_model = MockModel()
    results = gsm8k.evaluate_model(mock_model, 2)
    
    print(f"GSM8K Results:")
    print(f"  Accuracy: {results['accuracy']:.2%}")
    print(f"  Correct: {results['correct']}/{results['total_problems']}")
    
    # Test MATH
    print("\nTesting MATH benchmark...")
    math_bench = MATHBenchmark()
    math_bench.load_dataset(3)
    
    results = math_bench.evaluate_model(mock_model, 2)
    
    print(f"MATH Results:")
    print(f"  Accuracy: {results['accuracy']:.2%}")
    print(f"  Correct: {results['correct']}/{results['total_problems']}")
    
    if results['by_subject']:
        print("  By Subject:")
        for subject, data in results['by_subject'].items():
            print(f"    {subject}: {data['accuracy']:.2%} ({data['correct']}/{data['total']})")
