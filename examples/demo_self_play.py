#!/usr/bin/env python3
"""
Demo script showing a single iteration of the self-play process.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.model_interface import create_model_interface
from src.python_executor import PythonExecutor
from src.task_generator import TaskGenerator
from src.task_solver import TaskSolver
from src.reward_system import RewardSystem
from src.utils import format_task_for_display, format_solution_for_display


def demo_single_iteration():
    """Demonstrate a single iteration of the PROPOSE-SOLVE cycle."""
    
    print("=== Absolute Zero Reasoner - Self-Play Demo ===\n")
    
    # Initialize components
    print("Initializing components...")
    model = create_model_interface("microsoft/DialoGPT-small")
    executor = PythonExecutor(timeout=10)
    generator = TaskGenerator(model, executor)
    solver = TaskSolver(model, executor)
    
    # Reward system configuration
    reward_config = {
        'task_complexity_weight': 0.3,
        'task_clarity_weight': 0.2,
        'task_solvability_weight': 0.3,
        'task_diversity_weight': 0.2,
        'accuracy_weight': 0.6,
        'efficiency_weight': 0.2,
        'elegance_weight': 0.2,
    }
    reward_system = RewardSystem(reward_config)
    
    print("Components initialized!\n")
    
    # PROPOSE Phase
    print("="*60)
    print("PROPOSE PHASE - Generating Reasoning Tasks")
    print("="*60)
    
    # Generate a few tasks
    task_types = ['abduction', 'deduction', 'induction']
    generated_tasks = []
    task_rewards = []
    
    for i, task_type in enumerate(task_types, 1):
        print(f"\n--- Generating {task_type.upper()} Task {i} ---")
        
        try:
            # Generate task
            task = generator.generate_task(task_type, 'math')
            generated_tasks.append(task)
            
            print(format_task_for_display(task))
            
            # Validate task
            validation = generator.validate_task(task)
            print(f"Validation: Valid={validation['is_valid']}, Issues={validation['issues']}")
            
            # Calculate reward
            reward_components = reward_system.calculate_task_reward(task, validation)
            task_reward = reward_components.total_task_reward(reward_system.task_weights)
            task_rewards.append(task_reward)
            
            print(f"Task Reward: {task_reward:.4f}")
            print(f"  Complexity: {reward_components.task_complexity:.3f}")
            print(f"  Clarity: {reward_components.task_clarity:.3f}")
            print(f"  Solvability: {reward_components.task_solvability:.3f}")
            print(f"  Diversity: {reward_components.task_diversity:.3f}")
            
        except Exception as e:
            print(f"Error generating {task_type} task: {e}")
    
    print(f"\nPROPOSE Phase Summary:")
    print(f"Generated {len(generated_tasks)} tasks")
    print(f"Average task reward: {sum(task_rewards)/len(task_rewards):.4f}")
    
    # SOLVE Phase
    print("\n" + "="*60)
    print("SOLVE PHASE - Solving Generated Tasks")
    print("="*60)
    
    solution_rewards = []
    correct_solutions = 0
    
    for i, task in enumerate(generated_tasks, 1):
        print(f"\n--- Solving Task {i} ({task.task_type.upper()}) ---")
        print(f"Problem: {task.problem_statement[:100]}...")
        
        try:
            # Solve task
            solution = solver.solve_task(task)
            
            print(format_solution_for_display(solution))
            
            # Calculate reward
            reward_components = reward_system.calculate_solution_reward(solution)
            solution_reward = reward_components.total_solution_reward(reward_system.solution_weights)
            solution_rewards.append(solution_reward)
            
            if solution.is_correct:
                correct_solutions += 1
            
            print(f"Solution Reward: {solution_reward:.4f}")
            print(f"  Accuracy: {reward_components.solution_accuracy:.3f}")
            print(f"  Efficiency: {reward_components.solution_efficiency:.3f}")
            print(f"  Elegance: {reward_components.solution_elegance:.3f}")
            
            # Get quality metrics
            quality_metrics = solver.get_solution_quality_metrics(solution)
            print(f"Quality Score: {quality_metrics['quality_score']:.3f}")
            
        except Exception as e:
            print(f"Error solving task: {e}")
    
    print(f"\nSOLVE Phase Summary:")
    print(f"Attempted {len(generated_tasks)} solutions")
    print(f"Correct solutions: {correct_solutions}")
    print(f"Accuracy: {correct_solutions/len(generated_tasks):.4f}")
    print(f"Average solution reward: {sum(solution_rewards)/len(solution_rewards):.4f}")
    
    # Combined Analysis
    print("\n" + "="*60)
    print("ITERATION ANALYSIS")
    print("="*60)
    
    # Calculate combined training signal
    if task_rewards and solution_rewards:
        avg_task_reward = sum(task_rewards) / len(task_rewards)
        avg_solution_reward = sum(solution_rewards) / len(solution_rewards)
        combined_reward = (avg_task_reward + avg_solution_reward) / 2
        
        print(f"Average Task Generation Reward: {avg_task_reward:.4f}")
        print(f"Average Solution Reward: {avg_solution_reward:.4f}")
        print(f"Combined Training Signal: {combined_reward:.4f}")
        
        # Reward system statistics
        stats = reward_system.get_statistics()
        print(f"\nReward System Statistics:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {value}")
    
    print(f"\n{'='*60}")
    print("SELF-PLAY ITERATION COMPLETED")
    print(f"{'='*60}")


def demo_progressive_iterations():
    """Demonstrate multiple iterations showing improvement over time."""
    
    print("=== Progressive Self-Play Demo ===\n")
    
    # Initialize components
    model = create_model_interface("microsoft/DialoGPT-small")
    executor = PythonExecutor(timeout=10)
    generator = TaskGenerator(model, executor)
    solver = TaskSolver(model, executor)
    
    reward_config = {
        'task_complexity_weight': 0.3,
        'task_clarity_weight': 0.2,
        'task_solvability_weight': 0.3,
        'task_diversity_weight': 0.2,
        'accuracy_weight': 0.6,
        'efficiency_weight': 0.2,
        'elegance_weight': 0.2,
    }
    reward_system = RewardSystem(reward_config)
    
    # Track progress over iterations
    iteration_results = []
    
    num_iterations = 5
    tasks_per_iteration = 2
    
    for iteration in range(num_iterations):
        print(f"\n{'='*50}")
        print(f"ITERATION {iteration + 1}/{num_iterations}")
        print(f"{'='*50}")
        
        # Generate and solve tasks
        iteration_task_rewards = []
        iteration_solution_rewards = []
        iteration_accuracy = 0
        
        for task_num in range(tasks_per_iteration):
            print(f"\n--- Task {task_num + 1} ---")
            
            try:
                # Generate task
                task_type = ['abduction', 'deduction', 'induction'][task_num % 3]
                task = generator.generate_task(task_type, 'math')
                
                # Validate and reward task
                validation = generator.validate_task(task)
                task_reward_components = reward_system.calculate_task_reward(task, validation)
                task_reward = task_reward_components.total_task_reward(reward_system.task_weights)
                iteration_task_rewards.append(task_reward)
                
                print(f"Generated {task_type} task (reward: {task_reward:.3f})")
                print(f"Problem: {task.problem_statement[:80]}...")
                
                # Solve task
                solution = solver.solve_task(task)
                solution_reward_components = reward_system.calculate_solution_reward(solution)
                solution_reward = solution_reward_components.total_solution_reward(reward_system.solution_weights)
                iteration_solution_rewards.append(solution_reward)
                
                if solution.is_correct:
                    iteration_accuracy += 1
                
                print(f"Solution correct: {solution.is_correct} (reward: {solution_reward:.3f})")
                
            except Exception as e:
                print(f"Error in task {task_num + 1}: {e}")
        
        # Calculate iteration metrics
        avg_task_reward = sum(iteration_task_rewards) / len(iteration_task_rewards) if iteration_task_rewards else 0
        avg_solution_reward = sum(iteration_solution_rewards) / len(iteration_solution_rewards) if iteration_solution_rewards else 0
        accuracy = iteration_accuracy / tasks_per_iteration
        
        iteration_result = {
            'iteration': iteration + 1,
            'avg_task_reward': avg_task_reward,
            'avg_solution_reward': avg_solution_reward,
            'accuracy': accuracy,
            'combined_reward': (avg_task_reward + avg_solution_reward) / 2
        }
        iteration_results.append(iteration_result)
        
        print(f"\nIteration {iteration + 1} Summary:")
        print(f"  Task Reward: {avg_task_reward:.4f}")
        print(f"  Solution Reward: {avg_solution_reward:.4f}")
        print(f"  Accuracy: {accuracy:.4f}")
        print(f"  Combined Reward: {iteration_result['combined_reward']:.4f}")
    
    # Show progress over time
    print(f"\n{'='*60}")
    print("PROGRESS ANALYSIS")
    print(f"{'='*60}")
    
    print("\nIteration | Task Reward | Solution Reward | Accuracy | Combined")
    print("-" * 65)
    for result in iteration_results:
        print(f"    {result['iteration']:2d}    |    {result['avg_task_reward']:.4f}    |     {result['avg_solution_reward']:.4f}      |  {result['accuracy']:.4f}   |  {result['combined_reward']:.4f}")
    
    # Calculate trends
    if len(iteration_results) > 1:
        task_trend = iteration_results[-1]['avg_task_reward'] - iteration_results[0]['avg_task_reward']
        solution_trend = iteration_results[-1]['avg_solution_reward'] - iteration_results[0]['avg_solution_reward']
        accuracy_trend = iteration_results[-1]['accuracy'] - iteration_results[0]['accuracy']
        
        print(f"\nTrends (Last - First):")
        print(f"  Task Reward: {task_trend:+.4f}")
        print(f"  Solution Reward: {solution_trend:+.4f}")
        print(f"  Accuracy: {accuracy_trend:+.4f}")
    
    # Final statistics
    stats = reward_system.get_statistics()
    print(f"\nFinal Statistics:")
    print(f"  Total tasks generated: {stats.get('total_tasks_generated', 0)}")
    print(f"  Total solutions attempted: {stats.get('total_solutions_attempted', 0)}")
    print(f"  Recent accuracy rate: {stats.get('recent_accuracy_rate', 0):.4f}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Self-Play Demo')
    parser.add_argument('--progressive', action='store_true',
                       help='Run progressive iterations demo')
    
    args = parser.parse_args()
    
    if args.progressive:
        demo_progressive_iterations()
    else:
        demo_single_iteration()
