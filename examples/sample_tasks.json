[{"task_type": "abduction", "domain": "math", "difficulty": "easy", "problem_statement": "A sequence starts with the numbers 2, 6, 18, 54. What is the most likely rule generating this sequence?", "solution": "The most likely rule is that each term is multiplied by 3 to get the next term. This is a geometric sequence with common ratio 3: 2 × 3 = 6, 6 × 3 = 18, 18 × 3 = 54.", "metadata": {"expected_next_term": 162, "sequence_type": "geometric"}}, {"task_type": "deduction", "domain": "math", "difficulty": "medium", "problem_statement": "If x + 2y = 10 and 3x - y = 5, find the values of x and y.", "solution": "From the first equation: x = 10 - 2y. Substituting into the second equation: 3(10 - 2y) - y = 5. Expanding: 30 - 6y - y = 5. Simplifying: 30 - 7y = 5. Solving: 7y = 25, so y = 25/7. Substituting back: x = 10 - 2(25/7) = 10 - 50/7 = 20/7.", "metadata": {"x_value": "20/7", "y_value": "25/7", "verification": "x + 2y = 20/7 + 50/7 = 70/7 = 10 ✓"}}, {"task_type": "induction", "domain": "math", "difficulty": "easy", "problem_statement": "Given the sequence 1, 4, 9, 16, 25, what is the general formula and the next term?", "solution": "The pattern is perfect squares: 1² = 1, 2² = 4, 3² = 9, 4² = 16, 5² = 25. The general formula is f(n) = n². The next term is 6² = 36.", "metadata": {"formula": "n²", "next_term": 36, "pattern_type": "perfect_squares"}}, {"task_type": "abduction", "domain": "logic", "difficulty": "medium", "problem_statement": "All the lights in the house are off, the front door is unlocked, and there's a note on the kitchen table saying 'Back in 3 days'. What most likely happened?", "solution": "The most likely explanation is that the residents left for a short trip or vacation. They turned off all lights to save energy, left a note indicating their return time, but forgot to lock the front door in their hurry to leave.", "metadata": {"scenario": "vacation_departure", "evidence": ["lights_off", "door_unlocked", "note_present"]}}, {"task_type": "deduction", "domain": "code", "difficulty": "easy", "problem_statement": "What will this code output?\n```python\ndef mystery_function(n):\n    result = 1\n    for i in range(1, n + 1):\n        result *= i\n    return result\nprint(mystery_function(5))\n```", "solution": "This function calculates the factorial of n. For n=5: result starts at 1, then 1×1=1, 1×2=2, 2×3=6, 6×4=24, 24×5=120. The output will be 120.", "metadata": {"function_type": "factorial", "output": 120, "calculation_steps": ["1", "1×1=1", "1×2=2", "2×3=6", "6×4=24", "24×5=120"]}}, {"task_type": "induction", "domain": "code", "difficulty": "medium", "problem_statement": "Given these function calls and outputs, determine the pattern:\nf(1) = 1, f(2) = 4, f(3) = 9, f(4) = 16, f(5) = 25\nWhat does f(n) compute and what would f(7) return?", "solution": "The pattern shows that f(n) = n². Each input is squared: 1²=1, 2²=4, 3²=9, 4²=16, 5²=25. Therefore, f(7) = 7² = 49.", "metadata": {"pattern": "square_function", "formula": "n²", "f_7": 49}}, {"task_type": "abduction", "domain": "math", "difficulty": "hard", "problem_statement": "A ball is dropped from a height and bounces. The heights after each bounce are measured as 100m, 80m, 64m, 51.2m. What is the most likely coefficient of restitution?", "solution": "The coefficient of restitution can be found by examining the ratio between consecutive bounce heights. 80/100 = 0.8, 64/80 = 0.8, 51.2/64 = 0.8. The coefficient of restitution is 0.8, meaning the ball retains 80% of its height after each bounce.", "metadata": {"coefficient": 0.8, "bounce_ratios": [0.8, 0.8, 0.8], "physics_concept": "coefficient_of_restitution"}}, {"task_type": "deduction", "domain": "logic", "difficulty": "medium", "problem_statement": "All birds can fly. Penguins are birds. Penguins cannot fly. What can we conclude about this logical system?", "solution": "This logical system contains a contradiction. We have three statements: (1) All birds can fly, (2) Penguins are birds, (3) Penguins cannot fly. From (1) and (2), we can deduce that penguins can fly, but (3) states they cannot. Therefore, the system is inconsistent and at least one premise must be false.", "metadata": {"logical_error": "contradiction", "invalid_premise": "All birds can fly", "conclusion": "system_inconsistent"}}, {"task_type": "induction", "domain": "logic", "difficulty": "easy", "problem_statement": "Observe these statements: 'All observed swans are white', 'Swan #1 is white', 'Swan #2 is white', 'Swan #3 is white'. What general rule can we infer?", "solution": "Based on the observed evidence, we can inductively infer the general rule 'All swans are white'. However, this is a classic example of the problem of induction - the conclusion is not logically certain, as future observations might reveal non-white swans (like black swans).", "metadata": {"inferred_rule": "All swans are white", "logical_limitation": "problem_of_induction", "certainty": "probabilistic_not_absolute"}}, {"task_type": "deduction", "domain": "code", "difficulty": "hard", "problem_statement": "Given this recursive function, what will be the output for fib(6)?\n```python\ndef fib(n):\n    if n <= 1:\n        return n\n    return fib(n-1) + fib(n-2)\n```", "solution": "This is the <PERSON><PERSON><PERSON><PERSON> function. For fib(6): fib(6) = fib(5) + fib(4). Working backwards: fib(0)=0, fib(1)=1, fib(2)=1, fib(3)=2, fib(4)=3, fib(5)=5. Therefore fib(6) = 5 + 3 = 8.", "metadata": {"function_type": "<PERSON><PERSON><PERSON><PERSON>", "output": 8, "sequence": [0, 1, 1, 2, 3, 5, 8], "calculation": "fib(5) + fib(4) = 5 + 3 = 8"}}]