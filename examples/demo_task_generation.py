#!/usr/bin/env python3
"""
Demo script showing task generation capabilities.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.model_interface import create_model_interface
from src.python_executor import PythonExecutor
from src.task_generator import TaskGenerator
from src.utils import format_task_for_display


def demo_task_generation():
    """Demonstrate task generation for different reasoning types."""
    
    print("=== Absolute Zero Reasoner - Task Generation Demo ===\n")
    
    # Initialize components
    print("Initializing model and components...")
    model = create_model_interface("microsoft/DialoGPT-small")  # Small model for demo
    executor = PythonExecutor(timeout=5)
    generator = TaskGenerator(model, executor)
    
    print("Components initialized successfully!\n")
    
    # Generate tasks for each reasoning type
    reasoning_types = ['abduction', 'deduction', 'induction']
    domains = ['math', 'logic']
    
    for reasoning_type in reasoning_types:
        print(f"{'='*60}")
        print(f"GENERATING {reasoning_type.upper()} TASKS")
        print(f"{'='*60}")
        
        for domain in domains:
            print(f"\n--- {domain.upper()} DOMAIN ---")
            
            try:
                # Generate task
                task = generator.generate_task(reasoning_type, domain)
                
                # Display task
                print(format_task_for_display(task))
                
                # Validate task
                validation = generator.validate_task(task)
                print(f"Validation Result:")
                print(f"  Valid: {validation['is_valid']}")
                print(f"  Issues: {validation['issues']}")
                print(f"  Executable: {validation['executable']}")
                
                if validation['execution_result']:
                    exec_result = validation['execution_result']
                    print(f"  Execution Success: {exec_result['success']}")
                    if exec_result['output']:
                        print(f"  Output: {exec_result['output'][:100]}...")
                
            except Exception as e:
                print(f"Error generating {reasoning_type} task for {domain}: {e}")
            
            print("-" * 40)
    
    # Generate a batch of mixed tasks
    print(f"\n{'='*60}")
    print("GENERATING BATCH OF MIXED TASKS")
    print(f"{'='*60}")
    
    try:
        batch_tasks = generator.generate_batch(5)
        
        for i, task in enumerate(batch_tasks, 1):
            print(f"\n--- TASK {i} ---")
            print(f"Type: {task.task_type}, Domain: {task.domain}, Difficulty: {task.difficulty}")
            print(f"Problem: {task.problem_statement[:150]}...")
            print(f"Solution: {task.solution[:150]}...")
            
            # Quick validation
            validation = generator.validate_task(task)
            print(f"Valid: {validation['is_valid']}")
    
    except Exception as e:
        print(f"Error generating batch tasks: {e}")
    
    print(f"\n{'='*60}")
    print("DEMO COMPLETED")
    print(f"{'='*60}")


def interactive_task_generation():
    """Interactive task generation where user can specify parameters."""
    
    print("\n=== Interactive Task Generation ===")
    
    # Initialize components
    model = create_model_interface("microsoft/DialoGPT-small")
    executor = PythonExecutor(timeout=5)
    generator = TaskGenerator(model, executor)
    
    while True:
        print("\nChoose task type:")
        print("1. Abduction")
        print("2. Deduction") 
        print("3. Induction")
        print("4. Random")
        print("5. Exit")
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == '5':
            break
        
        # Map choice to task type
        task_type_map = {
            '1': 'abduction',
            '2': 'deduction', 
            '3': 'induction',
            '4': None  # Random
        }
        
        task_type = task_type_map.get(choice)
        if task_type is None and choice != '4':
            print("Invalid choice. Please try again.")
            continue
        
        # Choose domain
        print("\nChoose domain:")
        print("1. Math")
        print("2. Logic")
        print("3. Code")
        print("4. Random")
        
        domain_choice = input("Enter choice (1-4): ").strip()
        domain_map = {
            '1': 'math',
            '2': 'logic',
            '3': 'code',
            '4': None  # Random
        }
        
        domain = domain_map.get(domain_choice)
        
        try:
            # Generate task
            print("\nGenerating task...")
            if choice == '4':  # Random task type
                import random
                task_type = random.choice(['abduction', 'deduction', 'induction'])
            
            task = generator.generate_task(task_type, domain)
            
            # Display result
            print(format_task_for_display(task))
            
            # Ask if user wants validation
            validate = input("Validate this task? (y/n): ").strip().lower()
            if validate == 'y':
                validation = generator.validate_task(task)
                print(f"\nValidation Results:")
                print(f"Valid: {validation['is_valid']}")
                if validation['issues']:
                    print(f"Issues: {validation['issues']}")
                if validation['executable']:
                    print(f"Executable: {validation['executable']}")
        
        except Exception as e:
            print(f"Error generating task: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Task Generation Demo')
    parser.add_argument('--interactive', action='store_true',
                       help='Run interactive demo')
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_task_generation()
    else:
        demo_task_generation()
