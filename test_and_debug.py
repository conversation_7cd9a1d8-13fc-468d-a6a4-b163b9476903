#!/usr/bin/env python3
"""
Comprehensive testing and debugging script for Absolute Zero Reasoner.
Tests, debugs, and iterates until we achieve results similar to the paper.
"""

import sys
import os
import argparse
import json
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'benchmarks'))

from src.model_interface import create_model_interface, get_recommended_models
from src.utils import load_config
from benchmarks.evaluator import AbsoluteZeroEvaluator


def test_model_capabilities(model_name: str):
    """Test basic model capabilities before training."""
    print(f"🔍 Testing model capabilities: {model_name}")
    print("-" * 50)
    
    try:
        model = create_model_interface(model_name)
        
        # Test basic generation
        test_prompts = [
            "Complete this Python function:\ndef add_numbers(a, b):",
            "Solve this math problem: What is 15 + 27?",
            "Explain step by step: If x + 5 = 12, what is x?",
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\nTest {i}: {prompt[:50]}...")
            try:
                response = model.generate(prompt, max_length=200, temperature=0.1)
                print(f"Response: {response[:100]}...")
                print("✅ Generation successful")
            except Exception as e:
                print(f"❌ Generation failed: {e}")
                return False
        
        print("\n✅ Model capabilities test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False


def run_quick_benchmark(model_name: str):
    """Run a quick benchmark to establish baseline."""
    print(f"\n📊 Running quick benchmark: {model_name}")
    print("-" * 50)
    
    try:
        evaluator = AbsoluteZeroEvaluator()
        
        # Quick evaluation with fewer problems
        num_problems = {
            'humaneval': 5,
            'mbpp': 5,
            'gsm8k': 10,
            'math': 5
        }
        
        baseline_results = evaluator.evaluate_baseline(model_name, num_problems)
        
        print("\n📈 Quick Benchmark Results:")
        for bench_name, results in baseline_results['benchmarks'].items():
            if bench_name in ['humaneval', 'mbpp']:
                score = results['pass_at_1']
                metric = 'Pass@1'
            else:
                score = results['accuracy']
                metric = 'Accuracy'
            
            print(f"  {bench_name.upper()} {metric}: {score:.2%}")
        
        return baseline_results
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        return None


def run_training_experiment(model_name: str, iterations: int = 20):
    """Run a training experiment and compare results."""
    print(f"\n🎯 Running training experiment: {model_name}")
    print(f"Training iterations: {iterations}")
    print("-" * 50)
    
    try:
        evaluator = AbsoluteZeroEvaluator()
        
        # Run training with evaluation
        results = evaluator.train_and_evaluate(
            training_iterations=iterations,
            eval_intervals=[5, 10, iterations]
        )
        
        # Compare pre-training vs final
        pre_training = results['evaluations']['pre_training']
        final = results['evaluations']['final']
        
        print("\n📈 Training Results:")
        print("Benchmark | Pre-Training | Final | Improvement")
        print("-" * 50)
        
        improvements = []
        for bench_name in ['humaneval', 'mbpp', 'gsm8k', 'math']:
            if bench_name in pre_training['benchmarks'] and bench_name in final['benchmarks']:
                
                if bench_name in ['humaneval', 'mbpp']:
                    pre_score = pre_training['benchmarks'][bench_name]['pass_at_1']
                    final_score = final['benchmarks'][bench_name]['pass_at_1']
                else:
                    pre_score = pre_training['benchmarks'][bench_name]['accuracy']
                    final_score = final['benchmarks'][bench_name]['accuracy']
                
                improvement = final_score - pre_score
                improvements.append(improvement)
                
                print(f"{bench_name:9} | {pre_score:11.2%} | {final_score:5.2%} | {improvement:+.2%}")
        
        avg_improvement = sum(improvements) / len(improvements) if improvements else 0
        print(f"\nAverage Improvement: {avg_improvement:+.2%}")
        
        return results, avg_improvement
        
    except Exception as e:
        print(f"❌ Training experiment failed: {e}")
        return None, 0


def debug_training_issues(model_name: str):
    """Debug common training issues."""
    print(f"\n🔧 Debugging training issues: {model_name}")
    print("-" * 50)
    
    try:
        from src.trainer import AbsoluteZeroTrainer
        from src.utils import load_config
        
        config = load_config('config/config.yaml')
        config['model']['name'] = model_name
        
        trainer = AbsoluteZeroTrainer(config)
        
        # Test task generation
        print("Testing task generation...")
        try:
            propose_results = trainer._propose_phase(2)
            print(f"✅ Generated {propose_results['generated_tasks']} tasks")
            print(f"   Valid tasks: {propose_results['valid_tasks']}")
            print(f"   Average reward: {propose_results['avg_reward']:.4f}")
        except Exception as e:
            print(f"❌ Task generation failed: {e}")
            return False
        
        # Test task solving
        print("\nTesting task solving...")
        try:
            solve_results = trainer._solve_phase(2)
            print(f"✅ Attempted {solve_results['attempted_solutions']} solutions")
            print(f"   Correct solutions: {solve_results['correct_solutions']}")
            print(f"   Accuracy: {solve_results['accuracy']:.4f}")
            print(f"   Average reward: {solve_results['avg_reward']:.4f}")
        except Exception as e:
            print(f"❌ Task solving failed: {e}")
            return False
        
        # Test reward system
        print("\nTesting reward system...")
        try:
            stats = trainer.reward_system.get_statistics()
            print(f"✅ Reward system working")
            print(f"   Tasks generated: {stats.get('total_tasks_generated', 0)}")
            print(f"   Solutions attempted: {stats.get('total_solutions_attempted', 0)}")
        except Exception as e:
            print(f"❌ Reward system failed: {e}")
            return False
        
        print("\n✅ All training components working!")
        return True
        
    except Exception as e:
        print(f"❌ Training debug failed: {e}")
        return False


def suggest_improvements(results: dict, avg_improvement: float):
    """Suggest improvements based on results."""
    print(f"\n💡 Improvement Suggestions")
    print("-" * 50)
    
    if avg_improvement < 0.01:  # Less than 1% improvement
        print("⚠️  Low improvement detected. Suggestions:")
        print("   1. Try a larger model (1B+ parameters)")
        print("   2. Increase training iterations (100+)")
        print("   3. Tune reward system weights")
        print("   4. Improve task generation prompts")
        print("   5. Add curriculum learning")
    
    elif avg_improvement < 0.05:  # Less than 5% improvement
        print("📈 Moderate improvement. Suggestions:")
        print("   1. Increase training iterations")
        print("   2. Fine-tune reward weights")
        print("   3. Add more diverse task types")
        print("   4. Implement proper PPO algorithm")
    
    else:
        print("🎉 Good improvement! Suggestions:")
        print("   1. Scale to larger models")
        print("   2. Increase benchmark problem counts")
        print("   3. Add more sophisticated evaluation")
        print("   4. Compare against paper baselines")
    
    # Model-specific suggestions
    if results and 'model_name' in results:
        model_name = results['model_name']
        if 'small' in model_name.lower() or 'DialoGPT-small' in model_name:
            print("\n🔧 Model-specific suggestions:")
            print("   - Current model is too small for significant improvements")
            print("   - Recommended: Qwen2.5-Coder-1.5B or larger")
            print("   - Consider using API models (GPT-3.5, Claude) for better results")


def main():
    """Main testing and debugging workflow."""
    parser = argparse.ArgumentParser(description='Test and Debug Absolute Zero Reasoner')
    parser.add_argument('--model', type=str, default=None, help='Model name to test')
    parser.add_argument('--quick-test', action='store_true', help='Run quick capability test only')
    parser.add_argument('--benchmark-only', action='store_true', help='Run benchmark only')
    parser.add_argument('--training-iterations', type=int, default=20, help='Training iterations')
    parser.add_argument('--debug-only', action='store_true', help='Debug training components only')
    parser.add_argument('--list-models', action='store_true', help='List recommended models')
    
    args = parser.parse_args()
    
    if args.list_models:
        print("🤖 Recommended Models for Absolute Zero:")
        models = get_recommended_models()
        for category, model_list in models.items():
            print(f"\n{category.replace('_', ' ').title()}:")
            for model in model_list:
                print(f"  - {model}")
        return
    
    # Determine model to use
    if args.model:
        model_name = args.model
    else:
        config = load_config('config/config.yaml')
        model_name = config['model']['name']
    
    print(f"🚀 Testing Absolute Zero Reasoner")
    print(f"Model: {model_name}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Step 1: Test model capabilities
    if not test_model_capabilities(model_name):
        print("\n❌ Model capability test failed. Exiting.")
        return
    
    if args.quick_test:
        print("\n✅ Quick test completed successfully!")
        return
    
    # Step 2: Debug training components
    if args.debug_only:
        debug_training_issues(model_name)
        return
    
    # Step 3: Run benchmark
    baseline_results = run_quick_benchmark(model_name)
    if not baseline_results:
        print("\n❌ Benchmark failed. Exiting.")
        return
    
    if args.benchmark_only:
        print("\n✅ Benchmark completed successfully!")
        return
    
    # Step 4: Run training experiment
    training_results, avg_improvement = run_training_experiment(model_name, args.training_iterations)
    
    # Step 5: Analyze and suggest improvements
    if training_results:
        suggest_improvements(training_results, avg_improvement)
        
        # Save results
        results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump({
                'model_name': model_name,
                'baseline_results': baseline_results,
                'training_results': training_results,
                'avg_improvement': avg_improvement,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_file}")
    
    print(f"\n🎯 Testing completed!")
    
    # Final recommendations
    print(f"\n📋 Next Steps:")
    if avg_improvement > 0.02:
        print("   ✅ Good progress! Consider scaling to larger models")
    else:
        print("   🔧 Try different models or increase training iterations")
    print("   📊 Run full evaluation with: python benchmarks/evaluator.py")
    print("   🚀 For production: Use 7B+ models with 100+ iterations")


if __name__ == "__main__":
    main()
