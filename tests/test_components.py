#!/usr/bin/env python3
"""
Test script to verify all components work correctly.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import unittest
from unittest.mock import Mock, patch
import tempfile
import json

from src.python_executor import PythonExecutor
from src.model_interface import HuggingFaceModel, create_model_interface
from src.task_generator import TaskGenerator, ReasoningTask
from src.task_solver import TaskSolver, SolutionAttempt
from src.reward_system import RewardSystem, RewardComponents
from src.utils import load_config, save_json, load_json


class TestPythonExecutor(unittest.TestCase):
    """Test the Python executor component."""
    
    def setUp(self):
        self.executor = PythonExecutor(timeout=5)
    
    def test_safe_code_execution(self):
        """Test execution of safe code."""
        code = """
def add_numbers(a, b):
    return a + b

result = add_numbers(5, 3)
print(f"Result: {result}")
result
"""
        result = self.executor.execute_code(code)
        self.assertTrue(result['success'])
        self.assertEqual(result['result'], 8)
        self.assertIn("Result: 8", result['output'])
    
    def test_unsafe_code_detection(self):
        """Test detection of unsafe code."""
        unsafe_code = "import os; os.system('ls')"
        result = self.executor.execute_code(unsafe_code)
        self.assertFalse(result['success'])
        self.assertIn("Security violation", result['error'])
    
    def test_syntax_error_handling(self):
        """Test handling of syntax errors."""
        bad_code = "def broken_function(\nprint('missing closing parenthesis')"
        result = self.executor.execute_code(bad_code)
        self.assertFalse(result['success'])
        self.assertIn("Syntax error", result['error'])
    
    def test_task_solution_validation(self):
        """Test validation of task-solution pairs."""
        task = "Calculate the factorial of 5"
        solution = """
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n-1)

result = factorial(5)
print(f"Factorial of 5 is {result}")
result
"""
        validation = self.executor.validate_task_solution(task, solution)
        self.assertTrue(validation['is_valid'])
        self.assertTrue(validation['execution_result']['success'])


class TestTaskGenerator(unittest.TestCase):
    """Test the task generator component."""
    
    def setUp(self):
        # Mock model for testing
        self.mock_model = Mock()
        self.mock_model.generate.return_value = """
Problem: Find the value of x if 2x + 5 = 15
Solution: 2x = 15 - 5 = 10, so x = 5
"""
        self.executor = PythonExecutor(timeout=5)
        self.generator = TaskGenerator(self.mock_model, self.executor)
    
    def test_task_generation(self):
        """Test basic task generation."""
        task = self.generator.generate_task('deduction', 'math')
        
        self.assertEqual(task.task_type, 'deduction')
        self.assertEqual(task.domain, 'math')
        self.assertIsInstance(task.problem_statement, str)
        self.assertIsInstance(task.solution, str)
        self.assertIn(task.difficulty, ['easy', 'medium', 'hard'])
    
    def test_batch_generation(self):
        """Test batch task generation."""
        tasks = self.generator.generate_batch(3)
        
        self.assertEqual(len(tasks), 3)
        for task in tasks:
            self.assertIsInstance(task, ReasoningTask)
    
    def test_task_validation(self):
        """Test task validation."""
        task = ReasoningTask(
            task_type='deduction',
            problem_statement='Find x if 2x = 10',
            solution='x = 5',
            difficulty='easy',
            domain='math'
        )
        
        validation = self.generator.validate_task(task)
        self.assertIsInstance(validation, dict)
        self.assertIn('is_valid', validation)
        self.assertIn('issues', validation)


class TestTaskSolver(unittest.TestCase):
    """Test the task solver component."""
    
    def setUp(self):
        # Mock model for testing
        self.mock_model = Mock()
        self.mock_model.generate.return_value = """
Reasoning:
The equation is 2x + 5 = 15
Subtract 5 from both sides: 2x = 10
Divide by 2: x = 5

Answer:
x = 5
"""
        self.executor = PythonExecutor(timeout=5)
        self.solver = TaskSolver(self.mock_model, self.executor)
    
    def test_task_solving(self):
        """Test basic task solving."""
        task = ReasoningTask(
            task_type='deduction',
            problem_statement='Find x if 2x + 5 = 15',
            solution='x = 5',
            difficulty='easy',
            domain='math'
        )
        
        solution = self.solver.solve_task(task)
        
        self.assertIsInstance(solution, SolutionAttempt)
        self.assertEqual(solution.task, task)
        self.assertIsInstance(solution.generated_solution, str)
        self.assertIsInstance(solution.is_correct, bool)
        self.assertIsInstance(solution.confidence, float)
    
    def test_solution_quality_metrics(self):
        """Test solution quality metrics calculation."""
        task = ReasoningTask(
            task_type='deduction',
            problem_statement='Test problem',
            solution='Test solution',
            difficulty='easy',
            domain='math'
        )
        
        solution = SolutionAttempt(
            task=task,
            generated_solution='Test generated solution',
            is_correct=True,
            confidence=0.8,
            reasoning_steps=['Step 1', 'Step 2']
        )
        
        metrics = self.solver.get_solution_quality_metrics(solution)
        
        self.assertIn('correctness', metrics)
        self.assertIn('confidence', metrics)
        self.assertIn('quality_score', metrics)
        self.assertEqual(metrics['correctness'], 1.0)
        self.assertEqual(metrics['confidence'], 0.8)


class TestRewardSystem(unittest.TestCase):
    """Test the reward system component."""
    
    def setUp(self):
        config = {
            'task_complexity_weight': 0.3,
            'task_clarity_weight': 0.2,
            'task_solvability_weight': 0.3,
            'task_diversity_weight': 0.2,
            'accuracy_weight': 0.6,
            'efficiency_weight': 0.2,
            'elegance_weight': 0.2,
        }
        self.reward_system = RewardSystem(config)
    
    def test_task_reward_calculation(self):
        """Test task reward calculation."""
        task = ReasoningTask(
            task_type='deduction',
            problem_statement='Find the value of x in the equation 2x + 5 = 15',
            solution='Subtract 5 from both sides: 2x = 10. Divide by 2: x = 5.',
            difficulty='medium',
            domain='math'
        )
        
        validation_result = {'is_valid': True, 'issues': []}
        rewards = self.reward_system.calculate_task_reward(task, validation_result)
        
        self.assertIsInstance(rewards, RewardComponents)
        self.assertGreaterEqual(rewards.task_complexity, 0.0)
        self.assertLessEqual(rewards.task_complexity, 1.0)
        self.assertGreaterEqual(rewards.task_clarity, 0.0)
        self.assertLessEqual(rewards.task_clarity, 1.0)
    
    def test_solution_reward_calculation(self):
        """Test solution reward calculation."""
        task = ReasoningTask(
            task_type='deduction',
            problem_statement='Test problem',
            solution='Test solution',
            difficulty='easy',
            domain='math'
        )
        
        solution = SolutionAttempt(
            task=task,
            generated_solution='Well-reasoned solution with clear steps',
            is_correct=True,
            confidence=0.9,
            reasoning_steps=['Step 1', 'Step 2', 'Step 3']
        )
        
        rewards = self.reward_system.calculate_solution_reward(solution)
        
        self.assertIsInstance(rewards, RewardComponents)
        self.assertGreaterEqual(rewards.solution_accuracy, 0.0)
        self.assertLessEqual(rewards.solution_accuracy, 1.0)
    
    def test_training_signal_generation(self):
        """Test training signal generation."""
        task_rewards = RewardComponents(
            task_complexity=0.7,
            task_clarity=0.8,
            task_solvability=0.6,
            task_diversity=0.5
        )
        
        solution_rewards = RewardComponents(
            solution_accuracy=0.9,
            solution_efficiency=0.7,
            solution_elegance=0.6
        )
        
        signal = self.reward_system.get_training_signal(task_rewards, solution_rewards)
        
        self.assertIn('task_reward', signal)
        self.assertIn('solution_reward', signal)
        self.assertIn('combined_reward', signal)
        self.assertIn('individual_components', signal)


class TestUtils(unittest.TestCase):
    """Test utility functions."""
    
    def test_json_operations(self):
        """Test JSON save and load operations."""
        test_data = {'test': 'data', 'number': 42}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            # Test save
            save_json(test_data, temp_path)
            self.assertTrue(os.path.exists(temp_path))
            
            # Test load
            loaded_data = load_json(temp_path)
            self.assertEqual(loaded_data, test_data)
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)


def run_integration_test():
    """Run a simple integration test of the full pipeline."""
    print("Running integration test...")
    
    # Mock model for testing
    mock_model = Mock()
    mock_model.generate.side_effect = [
        # Task generation response
        """
Problem: Calculate the sum of first 5 natural numbers
Solution: 1 + 2 + 3 + 4 + 5 = 15
""",
        # Solution generation response
        """
Reasoning:
We need to add the first 5 natural numbers: 1, 2, 3, 4, 5
1 + 2 = 3
3 + 3 = 6  
6 + 4 = 10
10 + 5 = 15

Answer:
The sum is 15
"""
    ]
    
    # Initialize components
    executor = PythonExecutor(timeout=5)
    generator = TaskGenerator(mock_model, executor)
    solver = TaskSolver(mock_model, executor)
    
    reward_config = {
        'task_complexity_weight': 0.3,
        'task_clarity_weight': 0.2,
        'task_solvability_weight': 0.3,
        'task_diversity_weight': 0.2,
        'accuracy_weight': 0.6,
        'efficiency_weight': 0.2,
        'elegance_weight': 0.2,
    }
    reward_system = RewardSystem(reward_config)
    
    # Test full pipeline
    print("1. Generating task...")
    task = generator.generate_task('deduction', 'math')
    print(f"   Generated: {task.problem_statement[:50]}...")
    
    print("2. Validating task...")
    validation = generator.validate_task(task)
    print(f"   Valid: {validation['is_valid']}")
    
    print("3. Calculating task reward...")
    task_rewards = reward_system.calculate_task_reward(task, validation)
    task_reward = task_rewards.total_task_reward(reward_system.task_weights)
    print(f"   Task reward: {task_reward:.4f}")
    
    print("4. Solving task...")
    solution = solver.solve_task(task)
    print(f"   Solution correct: {solution.is_correct}")
    
    print("5. Calculating solution reward...")
    solution_rewards = reward_system.calculate_solution_reward(solution)
    solution_reward = solution_rewards.total_solution_reward(reward_system.solution_weights)
    print(f"   Solution reward: {solution_reward:.4f}")
    
    print("6. Generating training signal...")
    training_signal = reward_system.get_training_signal(task_rewards, solution_rewards)
    print(f"   Combined reward: {training_signal['combined_reward']:.4f}")
    
    print("Integration test completed successfully!")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Absolute Zero components')
    parser.add_argument('--integration', action='store_true',
                       help='Run integration test instead of unit tests')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    if args.integration:
        run_integration_test()
    else:
        # Run unit tests
        if args.verbose:
            unittest.main(verbosity=2)
        else:
            unittest.main()
