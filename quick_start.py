#!/usr/bin/env python3
"""
Quick start script for Absolute Zero Reasoner.
Demonstrates the key concepts with minimal setup.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.model_interface import create_model_interface
from src.python_executor import PythonExecutor
from src.task_generator import TaskGenerator
from src.task_solver import TaskSolver
from src.reward_system import RewardSystem


def quick_demo():
    """Run a quick demonstration of the Absolute Zero concepts."""
    
    print("🚀 Absolute Zero Reasoner - Quick Start Demo")
    print("=" * 60)
    print()
    
    print("📚 What is Absolute Zero?")
    print("Absolute Zero is a method for training AI models to improve reasoning")
    print("through self-play WITHOUT any external training data!")
    print()
    
    print("🔄 The Process:")
    print("1. PROPOSE: Model generates reasoning tasks")
    print("2. SOLVE: Model attempts to solve those tasks")
    print("3. REWARD: Both phases get rewards based on quality")
    print("4. LEARN: Model improves through reinforcement learning")
    print()
    
    print("🛠️ Setting up components...")
    
    # Initialize components (using a small model for demo)
    try:
        model = create_model_interface("microsoft/DialoGPT-small")
        print("✅ Model loaded")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        print("💡 For this demo, we'll simulate model responses")
        
        # Create a mock model for demonstration
        class MockModel:
            def generate(self, prompt, **kwargs):
                if "generate a mathematical" in prompt.lower():
                    return """
Problem: Find the value of x if 3x + 7 = 22
Solution: Subtract 7 from both sides: 3x = 15. Divide by 3: x = 5.
"""
                else:
                    return """
Reasoning:
The equation is 3x + 7 = 22
First, subtract 7 from both sides: 3x = 15
Then divide both sides by 3: x = 5

Answer:
x = 5
"""
        
        model = MockModel()
        print("✅ Mock model created for demo")
    
    executor = PythonExecutor(timeout=5)
    print("✅ Python executor ready")
    
    generator = TaskGenerator(model, executor)
    solver = TaskSolver(model, executor)
    print("✅ Task generator and solver ready")
    
    reward_config = {
        'task_complexity_weight': 0.3,
        'task_clarity_weight': 0.2,
        'task_solvability_weight': 0.3,
        'task_diversity_weight': 0.2,
        'accuracy_weight': 0.6,
        'efficiency_weight': 0.2,
        'elegance_weight': 0.2,
    }
    reward_system = RewardSystem(reward_config)
    print("✅ Reward system configured")
    print()
    
    # Demonstrate the PROPOSE-SOLVE cycle
    print("🎯 PROPOSE Phase - Generating a reasoning task...")
    print("-" * 40)
    
    try:
        task = generator.generate_task('deduction', 'math')
        print(f"📝 Generated Task Type: {task.task_type}")
        print(f"🏷️  Domain: {task.domain}")
        print(f"⚡ Difficulty: {task.difficulty}")
        print()
        print(f"❓ Problem: {task.problem_statement}")
        print()
        print(f"✅ Expected Solution: {task.solution}")
        print()
        
        # Validate the task
        validation = generator.validate_task(task)
        print(f"🔍 Task Validation: {'✅ Valid' if validation['is_valid'] else '❌ Invalid'}")
        if validation['issues']:
            print(f"⚠️  Issues: {validation['issues']}")
        
        # Calculate task reward
        task_rewards = reward_system.calculate_task_reward(task, validation)
        task_reward = task_rewards.total_task_reward(reward_system.task_weights)
        print(f"🏆 Task Reward: {task_reward:.4f}")
        print(f"   📊 Complexity: {task_rewards.task_complexity:.3f}")
        print(f"   📝 Clarity: {task_rewards.task_clarity:.3f}")
        print(f"   ✅ Solvability: {task_rewards.task_solvability:.3f}")
        print(f"   🌟 Diversity: {task_rewards.task_diversity:.3f}")
        print()
        
    except Exception as e:
        print(f"❌ Task generation failed: {e}")
        return
    
    print("🧠 SOLVE Phase - Attempting to solve the task...")
    print("-" * 40)
    
    try:
        solution = solver.solve_task(task)
        print(f"🤖 Generated Solution:")
        print(f"{solution.generated_solution}")
        print()
        print(f"✅ Correct: {solution.is_correct}")
        print(f"🎯 Confidence: {solution.confidence:.3f}")
        
        if solution.reasoning_steps:
            print(f"🧩 Reasoning Steps:")
            for i, step in enumerate(solution.reasoning_steps, 1):
                print(f"   {i}. {step}")
        print()
        
        # Calculate solution reward
        solution_rewards = reward_system.calculate_solution_reward(solution)
        solution_reward = solution_rewards.total_solution_reward(reward_system.solution_weights)
        print(f"🏆 Solution Reward: {solution_reward:.4f}")
        print(f"   🎯 Accuracy: {solution_rewards.solution_accuracy:.3f}")
        print(f"   ⚡ Efficiency: {solution_rewards.solution_efficiency:.3f}")
        print(f"   ✨ Elegance: {solution_rewards.solution_elegance:.3f}")
        print()
        
    except Exception as e:
        print(f"❌ Solution generation failed: {e}")
        return
    
    # Show combined training signal
    print("🎓 Training Signal - What the model learns from...")
    print("-" * 40)
    
    training_signal = reward_system.get_training_signal(task_rewards, solution_rewards)
    print(f"📈 Combined Reward: {training_signal['combined_reward']:.4f}")
    print(f"📝 Task Generation Reward: {training_signal['task_reward']:.4f}")
    print(f"🧠 Solution Reward: {training_signal['solution_reward']:.4f}")
    print()
    
    print("🔄 In real training, this process repeats thousands of times:")
    print("   • Model generates increasingly complex tasks")
    print("   • Model gets better at solving them")
    print("   • Rewards guide the learning process")
    print("   • No external data needed - pure self-improvement!")
    print()
    
    print("🎉 Demo completed! Key takeaways:")
    print("   ✨ Self-generated tasks provide training signal")
    print("   🎯 Rewards encourage both good tasks and good solutions")
    print("   🔄 Iterative process leads to continuous improvement")
    print("   🚀 Zero external data required!")
    print()
    
    print("📖 Next steps:")
    print("   • Run 'python examples/demo_task_generation.py' for more task examples")
    print("   • Run 'python examples/demo_self_play.py' for full self-play demo")
    print("   • Run 'python main.py' for actual training")
    print("   • Check the README.md for detailed documentation")


def show_paper_summary():
    """Show a summary of the original paper."""
    
    print("📄 Paper Summary: 'Absolute Zero: Reinforced Self-play Reasoning with Zero Data'")
    print("=" * 80)
    print()
    
    print("🎯 Main Contribution:")
    print("   A method for training language models to improve reasoning abilities")
    print("   through self-play without any external curated training data.")
    print()
    
    print("🔬 Key Innovation:")
    print("   Two-phase iterative process:")
    print("   1. PROPOSE: Generate reasoning tasks (abduction, deduction, induction)")
    print("   2. SOLVE: Attempt to solve the generated tasks")
    print("   Both phases use Python execution for validation and receive intrinsic rewards.")
    print()
    
    print("📊 Results:")
    print("   • Achieves competitive performance on code and math benchmarks")
    print("   • Shows consistent improvements across different model sizes")
    print("   • Demonstrates emergent cognitive patterns")
    print("   • Requires zero external training data")
    print()
    
    print("🏆 Performance Highlights:")
    print("   • AZR + Qwen2.5-7B-Coder: 61.6% code avg, 39.1% math avg")
    print("   • +5.0 improvement on code tasks, +15.2 on math tasks")
    print("   • Scales effectively from 3B to 14B parameters")
    print()
    
    print("🔧 Technical Details:")
    print("   • Uses TRR++ for reinforcement learning")
    print("   • Intrinsic rewards based on task quality and solution accuracy")
    print("   • Self-evolving curriculum that becomes progressively challenging")
    print("   • Safe Python execution environment for validation")
    print()
    
    print("🌟 Why This Matters:")
    print("   • Reduces dependence on curated datasets")
    print("   • Enables continuous self-improvement")
    print("   • Opens new possibilities for AI training")
    print("   • Demonstrates the power of intrinsic motivation in AI")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Absolute Zero Quick Start')
    parser.add_argument('--paper-summary', action='store_true',
                       help='Show paper summary instead of demo')
    
    args = parser.parse_args()
    
    if args.paper_summary:
        show_paper_summary()
    else:
        quick_demo()
