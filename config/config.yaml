# Absolute Zero Reasoner Configuration

# Model settings
model:
  name: "microsoft/DialoGPT-medium"  # Better model for actual results
  max_length: 512
  temperature: 0.7
  top_p: 0.9
  device: "auto"  # auto, cpu, cuda

# Training settings
training:
  max_iterations: 100
  batch_size: 4
  learning_rate: 1e-5
  warmup_steps: 10
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0
  
# Self-play settings
self_play:
  propose_solve_ratio: 0.5  # 50% propose, 50% solve
  max_task_length: 256
  max_solution_length: 512
  validation_timeout: 10  # seconds for Python execution
  
# Reward settings
rewards:
  # Task generation rewards
  task_complexity_weight: 0.3
  task_clarity_weight: 0.2
  task_solvability_weight: 0.3
  task_diversity_weight: 0.2
  
  # Solution rewards
  accuracy_weight: 0.6
  efficiency_weight: 0.2
  elegance_weight: 0.2
  
# Task generation prompts
prompts:
  abduction_prompt: |
    Generate a mathematical reasoning problem where you need to find the most likely explanation for given observations.
    Include the problem statement and a step-by-step solution.
    
  deduction_prompt: |
    Generate a logical reasoning problem where you need to derive conclusions from given premises.
    Include the problem statement and a step-by-step solution.
    
  induction_prompt: |
    Generate a pattern recognition problem where you need to infer general rules from specific examples.
    Include the problem statement and a step-by-step solution.

# Logging and output
logging:
  log_level: "INFO"
  log_file: "training.log"
  wandb_project: "absolute-zero-recreation"
  save_interval: 10  # Save model every N iterations
  
# Paths
paths:
  output_dir: "./outputs"
  model_save_dir: "./models"
  data_dir: "./data"
  logs_dir: "./logs"
