#!/usr/bin/env python3
"""
Main entry point for Absolute Zero Reasoner training.
"""

import argparse
import logging
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.trainer import AbsoluteZeroTrainer
from src.utils import load_config, set_random_seed, setup_directories, generate_training_report

logger = logging.getLogger(__name__)


def main():
    """Main function to run Absolute Zero training."""
    
    parser = argparse.ArgumentParser(description='Absolute Zero Reasoner Training')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    parser.add_argument('--no-wandb', action='store_true',
                       help='Disable Weights & Biases logging')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Load configuration
        logger.info(f"Loading configuration from {args.config}")
        config = load_config(args.config)
        
        # Disable wandb if requested
        if args.no_wandb:
            config['logging']['wandb_project'] = None
        
        # Set random seed
        set_random_seed(args.seed)
        logger.info(f"Set random seed to {args.seed}")
        
        # Setup directories
        setup_directories(config)
        
        # Create and run trainer
        logger.info("Initializing Absolute Zero Trainer...")
        trainer = AbsoluteZeroTrainer(config)
        
        logger.info("Starting training...")
        final_stats = trainer.train()
        
        # Generate training report
        report = generate_training_report(config, trainer.training_history, final_stats)
        
        # Save report
        report_path = os.path.join(config['paths']['output_dir'], 'training_report.md')
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"Training completed successfully!")
        logger.info(f"Final accuracy: {final_stats.get('final_accuracy', 0):.4f}")
        logger.info(f"Training report saved to: {report_path}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
