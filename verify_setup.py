#!/usr/bin/env python3
"""
Simple verification script to test the setup.
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.python_executor import PythonExecutor
        print("✅ PythonExecutor imported")
        
        from src.model_interface import create_model_interface
        print("✅ ModelInterface imported")
        
        from src.task_generator import TaskGenerator, ReasoningTask
        print("✅ TaskGenerator imported")
        
        from src.task_solver import TaskSolver, SolutionAttempt
        print("✅ TaskSolver imported")
        
        from src.reward_system import RewardSystem, RewardComponents
        print("✅ RewardSystem imported")
        
        from src.trainer import AbsoluteZeroTrainer
        print("✅ AbsoluteZeroTrainer imported")
        
        from src.utils import load_config
        print("✅ Utils imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_python_executor():
    """Test the Python executor."""
    print("\nTesting Python executor...")
    
    try:
        from src.python_executor import PythonExecutor
        
        executor = PythonExecutor(timeout=5)
        
        # Test safe code
        result = executor.execute_code("print('Hello, World!'); 2 + 2")
        if result['success'] and result['result'] == 4:
            print("✅ Python executor works correctly")
            return True
        else:
            print(f"❌ Python executor failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Python executor test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading."""
    print("\nTesting configuration loading...")
    
    try:
        from src.utils import load_config
        
        config = load_config('config/config.yaml')
        
        if 'model' in config and 'training' in config:
            print("✅ Configuration loaded successfully")
            return True
        else:
            print("❌ Configuration missing required sections")
            return False
            
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_mock_workflow():
    """Test a mock workflow without actual model."""
    print("\nTesting mock workflow...")
    
    try:
        from src.python_executor import PythonExecutor
        from src.task_generator import TaskGenerator, ReasoningTask
        from src.task_solver import TaskSolver, SolutionAttempt
        from src.reward_system import RewardSystem
        
        # Mock model
        class MockModel:
            def generate(self, prompt, **kwargs):
                return "Problem: 2 + 2 = ?\nSolution: 2 + 2 = 4"
        
        # Initialize components
        model = MockModel()
        executor = PythonExecutor(timeout=5)
        generator = TaskGenerator(model, executor)
        solver = TaskSolver(model, executor)
        
        reward_config = {
            'task_complexity_weight': 0.3,
            'task_clarity_weight': 0.2,
            'task_solvability_weight': 0.3,
            'task_diversity_weight': 0.2,
            'accuracy_weight': 0.6,
            'efficiency_weight': 0.2,
            'elegance_weight': 0.2,
        }
        reward_system = RewardSystem(reward_config)
        
        # Test task generation
        task = generator.generate_task('deduction', 'math')
        print(f"✅ Task generated: {task.task_type}")
        
        # Test task validation
        validation = generator.validate_task(task)
        print(f"✅ Task validated: {validation['is_valid']}")
        
        # Test reward calculation
        task_rewards = reward_system.calculate_task_reward(task, validation)
        print(f"✅ Task reward calculated: {task_rewards.task_complexity:.3f}")
        
        # Test solution generation
        solution = solver.solve_task(task)
        print(f"✅ Solution generated: {len(solution.generated_solution)} chars")
        
        # Test solution reward
        solution_rewards = reward_system.calculate_solution_reward(solution)
        print(f"✅ Solution reward calculated: {solution_rewards.solution_accuracy:.3f}")
        
        print("✅ Mock workflow completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Mock workflow failed: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🔍 Absolute Zero Reasoner - Setup Verification")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_python_executor,
        test_config_loading,
        test_mock_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Setup is working correctly.")
        print("\n📖 Next steps:")
        print("   • Run 'python3 quick_start.py' for a demo")
        print("   • Run 'python3 examples/demo_task_generation.py' for task examples")
        print("   • Run 'python3 main.py' for actual training")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
