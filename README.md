# Absolute Zero Reasoner Recreation

A simplified recreation of "Absolute Zero: Reinforced Self-play Reasoning with Zero Data" by <PERSON> et al. (2025).

## 📖 Paper Overview

The Absolute Zero Reasoner (AZR) is a groundbreaking approach that enables language models to improve their reasoning abilities through self-play without any external curated training data. The key innovation is a two-phase iterative process:

### 🔄 Core Algorithm

1. **PROPOSE Phase**: The model generates reasoning tasks from three types:
   - **Abduction**: Given observations, infer the most likely explanation
   - **Deduction**: Given premises, derive logical conclusions  
   - **Induction**: Given examples, infer general patterns

2. **SOLVE Phase**: The model attempts to solve the self-generated tasks

Both phases use Python execution for validation and receive rewards based on:
- **Learnability Reward**: How well-formed and solvable the generated tasks are
- **Accuracy Reward**: How correctly the model solves the tasks

### 🎯 Key Contributions

- **Zero External Data**: No curated datasets required - the model bootstraps from itself
- **Self-Evolving Curriculum**: Tasks become progressively more challenging
- **Intrinsic Motivation**: Rewards based on task quality and solution accuracy
- **Strong Performance**: Achieves competitive results on code and math benchmarks

## 🏗️ Project Structure

```
absolute_zero_recreation/
├── src/
│   ├── task_generator.py      # PROPOSE phase implementation
│   ├── task_solver.py         # SOLVE phase implementation  
│   ├── python_executor.py     # Safe Python execution environment
│   ├── reward_system.py       # Reward calculation logic
│   ├── trainer.py            # Main training loop with RL
│   ├── model_interface.py    # Interface for different LLMs
│   └── utils.py              # Helper functions
├── config/
│   └── config.yaml           # Configuration settings
├── examples/
│   ├── demo_task_generation.py
│   ├── demo_self_play.py
│   └── sample_tasks.json
├── tests/
│   └── test_components.py
├── requirements.txt
├── main.py                   # Main entry point
└── README.md
```

## 🚀 Quick Start

### Installation

```bash
# Clone or download this recreation
git clone <this-repo> absolute-zero-recreation
cd absolute-zero-recreation

# Install dependencies
pip install -r requirements.txt
```

### Instant Demo

```bash
# See the concepts in action (works without GPU)
python quick_start.py

# Show paper summary
python quick_start.py --paper-summary
```

### Step-by-Step Exploration

```bash
# 1. Explore task generation
python examples/demo_task_generation.py
python examples/demo_task_generation.py --interactive

# 2. See self-play in action
python examples/demo_self_play.py
python examples/demo_self_play.py --progressive

# 3. Test all components
python tests/test_components.py
python tests/test_components.py --integration

# 4. Run actual training
python main.py --config config/config.yaml
```

### Basic Usage in Code

```python
import yaml
from src.trainer import AbsoluteZeroTrainer

# Load configuration
with open('config/config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# Initialize trainer
trainer = AbsoluteZeroTrainer(config)

# Run self-play training
results = trainer.train()
print(f"Final accuracy: {results['final_accuracy']:.4f}")
```

## 🔧 Key Components

### Task Generator (PROPOSE Phase)
Generates three types of reasoning tasks:
- Mathematical problems with step-by-step solutions
- Code generation and debugging tasks
- Logical reasoning puzzles

### Python Executor
Safe execution environment that:
- Validates generated code
- Executes solutions with timeout protection
- Returns structured results and error handling

### Reward System
Calculates intrinsic rewards:
- **Task Quality**: Complexity, clarity, solvability
- **Solution Accuracy**: Correctness of generated solutions
- **Diversity**: Encourages varied task types

### Training Loop
Implements simplified reinforcement learning:
- Policy gradient updates based on rewards
- Alternates between PROPOSE and SOLVE phases
- Tracks performance metrics and convergence

## 📊 Expected Results

With sufficient training, you should observe:
- Increasing task complexity over time
- Improving solution accuracy
- Emergence of diverse reasoning patterns
- Self-correcting behavior

## 🔬 Research Extensions

This recreation provides a foundation for exploring:
- Different reward formulations
- Alternative task generation strategies
- Multi-modal reasoning tasks
- Scaling to larger models

## 📚 References

```bibtex
@misc{zhao2025absolutezeroreinforcedselfplay,
      title={Absolute Zero: Reinforced Self-play Reasoning with Zero Data}, 
      author={Andrew Zhao and Yiran Wu and Yang Yue and Tong Wu and Quentin Xu and Yang Yue and Matthieu Lin and Shenzhi Wang and Qingyun Wu and Zilong Zheng and Gao Huang},
      year={2025},
      eprint={2505.03335},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/2505.03335}, 
}
```

## ⚠️ Limitations & Future Work

### Current Limitations
This recreation is simplified for educational purposes:

- **Model Interface**: Uses basic HuggingFace models instead of optimized training setup
- **RL Algorithm**: Simplified reward-based learning instead of full TRR++
- **Execution Environment**: Basic Python executor (not production-safe)
- **Scale**: Designed for small models and short training runs
- **Evaluation**: Limited benchmarking compared to paper's comprehensive evaluation

### Differences from Original Paper
- Original uses sophisticated RL algorithms (TRR++) vs. our simplified approach
- Original has advanced safety measures vs. our basic Python executor
- Original trains on large-scale infrastructure vs. our local setup
- Original includes comprehensive evaluation on multiple benchmarks

### Future Improvements
- [ ] Implement proper PPO/TRR++ algorithm
- [ ] Add secure code execution environment
- [ ] Scale to larger models (7B+)
- [ ] Add comprehensive evaluation suite
- [ ] Implement advanced reward formulations
- [ ] Add multi-modal reasoning tasks

### Educational Value
Despite limitations, this recreation demonstrates:
✅ Core self-play concepts
✅ Task generation and validation
✅ Reward system design
✅ PROPOSE-SOLVE cycle
✅ Intrinsic motivation principles

## ⚠️ Disclaimer

This is an educational recreation for understanding the concepts. The original paper uses more sophisticated techniques including TRR++, advanced reward systems, and larger-scale infrastructure.
