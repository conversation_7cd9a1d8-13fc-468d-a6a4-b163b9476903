"""
Task Generator - PROPOSE Phase Implementation
Generates reasoning tasks using abduction, deduction, and induction.
"""

import random
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import re
import logging

from .model_interface import ModelInterface
from .python_executor import PythonExecutor

logger = logging.getLogger(__name__)


@dataclass
class ReasoningTask:
    """Data class for a reasoning task."""
    task_type: str  # 'abduction', 'deduction', 'induction'
    problem_statement: str
    solution: str
    difficulty: str  # 'easy', 'medium', 'hard'
    domain: str  # 'math', 'logic', 'code'
    metadata: Dict[str, Any] = None


class TaskGenerator:
    """Generates reasoning tasks for the PROPOSE phase."""
    
    def __init__(self, model: ModelInterface, executor: PythonExecutor):
        self.model = model
        self.executor = executor
        
        # Task generation prompts for different reasoning types
        self.prompts = {
            'abduction': {
                'math': """Generate a mathematical problem where you need to find the most likely explanation for given observations.

Example format:
Problem: Given that a sequence starts with 2, 6, 18, 54, what is the most likely rule generating this sequence?
Solution: The most likely rule is that each term is multiplied by 3 to get the next term (geometric sequence with ratio 3).

Generate a similar problem with a clear solution:""",
                
                'logic': """Generate a logical reasoning problem where you need to infer the most likely explanation.

Example format:
Problem: All the lights in the house are off, the front door is unlocked, and there's a note on the kitchen table. What most likely happened?
Solution: The residents likely left for a trip and forgot to lock the door, leaving a note for someone.

Generate a similar problem with a clear solution:""",
            },
            
            'deduction': {
                'math': """Generate a mathematical problem where you derive conclusions from given premises.

Example format:
Problem: If x + 2y = 10 and 3x - y = 5, find the values of x and y.
Solution: 
From the first equation: x = 10 - 2y
Substituting into the second: 3(10 - 2y) - y = 5
30 - 6y - y = 5
30 - 7y = 5
7y = 25
y = 25/7
x = 10 - 2(25/7) = 10 - 50/7 = 20/7

Generate a similar problem with step-by-step solution:""",
                
                'code': """Generate a coding problem where you need to derive the output from given code logic.

Example format:
Problem: What will this code output?
```python
def mystery_function(n):
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result
print(mystery_function(5))
```
Solution: This function calculates factorial. For n=5: 1*1*2*3*4*5 = 120

Generate a similar problem with clear solution:""",
            },
            
            'induction': {
                'math': """Generate a pattern recognition problem where you infer general rules from examples.

Example format:
Problem: Given the sequence 1, 4, 9, 16, 25, what is the general formula and the next term?
Solution: The pattern is n² (perfect squares). The general formula is f(n) = n². The next term is 6² = 36.

Generate a similar problem with clear pattern and solution:""",
                
                'code': """Generate a programming problem where you need to identify patterns in code behavior.

Example format:
Problem: Given these function calls and outputs, determine the pattern:
f(1) = 1, f(2) = 4, f(3) = 9, f(4) = 16
What does f(n) compute?
Solution: f(n) = n². The function computes the square of its input.

Generate a similar problem with clear pattern:""",
            }
        }
    
    def generate_task(self, task_type: str, domain: str = None) -> ReasoningTask:
        """Generate a single reasoning task."""
        if domain is None:
            domain = random.choice(['math', 'logic', 'code'])
        
        # Get appropriate prompt
        if task_type in self.prompts and domain in self.prompts[task_type]:
            prompt = self.prompts[task_type][domain]
        else:
            # Fallback to math domain
            prompt = self.prompts[task_type]['math']
        
        # Generate task using the model
        generated_text = self.model.generate(
            prompt, 
            max_length=400,
            temperature=0.8,
            top_p=0.9
        )
        
        # Parse the generated text to extract problem and solution
        problem, solution = self._parse_generated_task(generated_text)
        
        # Determine difficulty (simple heuristic)
        difficulty = self._estimate_difficulty(problem, solution)
        
        return ReasoningTask(
            task_type=task_type,
            problem_statement=problem,
            solution=solution,
            difficulty=difficulty,
            domain=domain,
            metadata={'generated_text': generated_text}
        )
    
    def generate_batch(self, batch_size: int, task_types: List[str] = None) -> List[ReasoningTask]:
        """Generate a batch of reasoning tasks."""
        if task_types is None:
            task_types = ['abduction', 'deduction', 'induction']
        
        tasks = []
        for _ in range(batch_size):
            task_type = random.choice(task_types)
            domain = random.choice(['math', 'logic', 'code'])
            
            try:
                task = self.generate_task(task_type, domain)
                tasks.append(task)
            except Exception as e:
                logger.warning(f"Failed to generate task: {e}")
                continue
        
        return tasks
    
    def _parse_generated_task(self, generated_text: str) -> Tuple[str, str]:
        """Parse generated text to extract problem and solution."""
        # Look for Problem: and Solution: patterns
        problem_match = re.search(r'Problem:\s*(.*?)(?=Solution:|$)', generated_text, re.DOTALL | re.IGNORECASE)
        solution_match = re.search(r'Solution:\s*(.*?)$', generated_text, re.DOTALL | re.IGNORECASE)
        
        if problem_match and solution_match:
            problem = problem_match.group(1).strip()
            solution = solution_match.group(1).strip()
        else:
            # Fallback: split by common patterns
            lines = generated_text.strip().split('\n')
            problem_lines = []
            solution_lines = []
            in_solution = False
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if any(keyword in line.lower() for keyword in ['solution:', 'answer:', 'explanation:']):
                    in_solution = True
                    if ':' in line:
                        solution_lines.append(line.split(':', 1)[1].strip())
                    continue
                
                if in_solution:
                    solution_lines.append(line)
                else:
                    problem_lines.append(line)
            
            problem = ' '.join(problem_lines) if problem_lines else "Generated problem"
            solution = ' '.join(solution_lines) if solution_lines else "Generated solution"
        
        return problem, solution
    
    def _estimate_difficulty(self, problem: str, solution: str) -> str:
        """Estimate task difficulty based on length and complexity."""
        total_length = len(problem) + len(solution)
        
        # Simple heuristic based on length and keywords
        complexity_keywords = [
            'equation', 'derivative', 'integral', 'matrix', 'algorithm',
            'recursive', 'optimization', 'probability', 'statistics'
        ]
        
        complexity_score = sum(1 for keyword in complexity_keywords 
                             if keyword in (problem + solution).lower())
        
        if total_length < 200 and complexity_score == 0:
            return 'easy'
        elif total_length < 400 and complexity_score <= 2:
            return 'medium'
        else:
            return 'hard'
    
    def validate_task(self, task: ReasoningTask) -> Dict[str, Any]:
        """Validate a generated task using the Python executor."""
        validation_result = {
            'is_valid': True,
            'issues': [],
            'executable': False,
            'execution_result': None
        }
        
        # Check if task has meaningful content
        if len(task.problem_statement.strip()) < 10:
            validation_result['is_valid'] = False
            validation_result['issues'].append("Problem statement too short")
        
        if len(task.solution.strip()) < 10:
            validation_result['is_valid'] = False
            validation_result['issues'].append("Solution too short")
        
        # Try to execute if it looks like code
        if task.domain == 'code' or 'python' in task.solution.lower():
            # Extract code blocks from solution
            code_blocks = re.findall(r'```python\n(.*?)\n```', task.solution, re.DOTALL)
            if not code_blocks:
                # Look for code without markdown
                code_blocks = re.findall(r'def\s+\w+.*?(?=\n\n|\n[A-Z]|$)', task.solution, re.DOTALL)
            
            if code_blocks:
                try:
                    execution_result = self.executor.execute_code(code_blocks[0])
                    validation_result['executable'] = True
                    validation_result['execution_result'] = execution_result
                    
                    if not execution_result['success']:
                        validation_result['issues'].append(f"Code execution failed: {execution_result['error']}")
                except Exception as e:
                    validation_result['issues'].append(f"Code validation error: {str(e)}")
        
        return validation_result


# Example usage and testing
if __name__ == "__main__":
    from .model_interface import create_model_interface
    
    # Create model and executor
    model = create_model_interface("microsoft/DialoGPT-small")
    executor = PythonExecutor()
    
    # Create task generator
    generator = TaskGenerator(model, executor)
    
    # Generate sample tasks
    print("Generating sample tasks...")
    
    for task_type in ['abduction', 'deduction', 'induction']:
        print(f"\n=== {task_type.upper()} TASK ===")
        task = generator.generate_task(task_type, 'math')
        print(f"Problem: {task.problem_statement}")
        print(f"Solution: {task.solution}")
        print(f"Difficulty: {task.difficulty}")
        
        # Validate task
        validation = generator.validate_task(task)
        print(f"Valid: {validation['is_valid']}")
        if validation['issues']:
            print(f"Issues: {validation['issues']}")
