"""
Utility functions for the Absolute Zero Reasoner.
"""

import json
import yaml
import os
import random
import numpy as np
import torch
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def save_json(data: Any, filepath: str, indent: int = 2):
    """Save data to JSON file."""
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=indent, default=str)


def load_json(filepath: str) -> Any:
    """Load data from JSON file."""
    with open(filepath, 'r') as f:
        return json.load(f)


def set_random_seed(seed: int = 42):
    """Set random seed for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


def setup_directories(config: Dict[str, Any]):
    """Create necessary directories based on config."""
    paths = config.get('paths', {})
    for path_key, path_value in paths.items():
        if path_value:
            os.makedirs(path_value, exist_ok=True)
            logger.info(f"Created directory: {path_value}")


def format_task_for_display(task) -> str:
    """Format a reasoning task for display."""
    return f"""
Task Type: {task.task_type.upper()}
Domain: {task.domain}
Difficulty: {task.difficulty}

Problem:
{task.problem_statement}

Solution:
{task.solution}
"""


def format_solution_for_display(solution) -> str:
    """Format a solution attempt for display."""
    return f"""
Generated Solution:
{solution.generated_solution}

Correct: {solution.is_correct}
Confidence: {solution.confidence:.3f}

Reasoning Steps:
{chr(10).join(f"  {i+1}. {step}" for i, step in enumerate(solution.reasoning_steps or []))}
"""


def calculate_moving_average(values: List[float], window_size: int = 10) -> List[float]:
    """Calculate moving average of values."""
    if len(values) < window_size:
        return values
    
    moving_avg = []
    for i in range(len(values)):
        start_idx = max(0, i - window_size + 1)
        window = values[start_idx:i+1]
        moving_avg.append(sum(window) / len(window))
    
    return moving_avg


def extract_code_blocks(text: str) -> List[str]:
    """Extract Python code blocks from text."""
    import re
    
    # Look for markdown code blocks
    code_blocks = re.findall(r'```python\n(.*?)\n```', text, re.DOTALL)
    
    if not code_blocks:
        # Look for code without markdown
        code_blocks = re.findall(r'def\s+\w+.*?(?=\n\n|\n[A-Z]|$)', text, re.DOTALL)
    
    return code_blocks


def extract_mathematical_expressions(text: str) -> List[str]:
    """Extract mathematical expressions from text."""
    import re
    
    # Look for equations and mathematical expressions
    patterns = [
        r'\$.*?\$',  # LaTeX inline math
        r'\$\$.*?\$\$',  # LaTeX display math
        r'[a-zA-Z]\s*=\s*[^,\n]+',  # Variable assignments
        r'\d+\s*[+\-*/]\s*\d+',  # Simple arithmetic
    ]
    
    expressions = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        expressions.extend(matches)
    
    return expressions


def validate_model_output(output: str, expected_format: str = None) -> Dict[str, Any]:
    """Validate model output format and content."""
    validation_result = {
        'is_valid': True,
        'issues': [],
        'length': len(output),
        'has_reasoning': False,
        'has_answer': False
    }
    
    # Check minimum length
    if len(output.strip()) < 10:
        validation_result['is_valid'] = False
        validation_result['issues'].append("Output too short")
    
    # Check for reasoning indicators
    reasoning_keywords = ['because', 'since', 'therefore', 'thus', 'reasoning', 'step']
    if any(keyword in output.lower() for keyword in reasoning_keywords):
        validation_result['has_reasoning'] = True
    
    # Check for answer indicators
    answer_keywords = ['answer', 'result', 'solution', 'conclusion']
    if any(keyword in output.lower() for keyword in answer_keywords):
        validation_result['has_answer'] = True
    
    # Format-specific validation
    if expected_format == 'code':
        code_blocks = extract_code_blocks(output)
        if not code_blocks:
            validation_result['issues'].append("No code blocks found")
    
    elif expected_format == 'math':
        math_expressions = extract_mathematical_expressions(output)
        if not math_expressions:
            validation_result['issues'].append("No mathematical expressions found")
    
    return validation_result


def create_training_summary(training_history: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create a summary of training progress."""
    if not training_history:
        return {'message': 'No training history available'}
    
    # Extract metrics by phase
    propose_rewards = []
    solve_rewards = []
    solve_accuracies = []
    
    for entry in training_history:
        results = entry['results']
        if entry['phase'] == 'PROPOSE':
            propose_rewards.append(results['avg_reward'])
        elif entry['phase'] == 'SOLVE':
            solve_rewards.append(results['avg_reward'])
            solve_accuracies.append(results.get('accuracy', 0))
    
    summary = {
        'total_iterations': len(training_history),
        'propose_iterations': len(propose_rewards),
        'solve_iterations': len(solve_rewards),
    }
    
    if propose_rewards:
        summary.update({
            'avg_propose_reward': np.mean(propose_rewards),
            'propose_reward_trend': calculate_moving_average(propose_rewards, 5)[-1] if len(propose_rewards) >= 5 else np.mean(propose_rewards),
        })
    
    if solve_rewards:
        summary.update({
            'avg_solve_reward': np.mean(solve_rewards),
            'solve_reward_trend': calculate_moving_average(solve_rewards, 5)[-1] if len(solve_rewards) >= 5 else np.mean(solve_rewards),
        })
    
    if solve_accuracies:
        summary.update({
            'avg_accuracy': np.mean(solve_accuracies),
            'accuracy_trend': calculate_moving_average(solve_accuracies, 5)[-1] if len(solve_accuracies) >= 5 else np.mean(solve_accuracies),
            'best_accuracy': max(solve_accuracies),
        })
    
    return summary


def generate_training_report(config: Dict[str, Any], training_history: List[Dict[str, Any]], 
                           final_stats: Dict[str, Any]) -> str:
    """Generate a comprehensive training report."""
    
    summary = create_training_summary(training_history)
    
    report = f"""
# Absolute Zero Reasoner Training Report

## Configuration
- Model: {config['model']['name']}
- Max Iterations: {config['training']['max_iterations']}
- Batch Size: {config['training']['batch_size']}
- Learning Rate: {config['training']['learning_rate']}

## Training Summary
- Total Iterations: {summary.get('total_iterations', 0)}
- PROPOSE Iterations: {summary.get('propose_iterations', 0)}
- SOLVE Iterations: {summary.get('solve_iterations', 0)}

## Performance Metrics
- Average PROPOSE Reward: {summary.get('avg_propose_reward', 0):.4f}
- Average SOLVE Reward: {summary.get('avg_solve_reward', 0):.4f}
- Average Accuracy: {summary.get('avg_accuracy', 0):.4f}
- Best Accuracy: {summary.get('best_accuracy', 0):.4f}

## Final Evaluation
- Final Accuracy: {final_stats.get('final_accuracy', 0):.4f}
- Final Confidence: {final_stats.get('final_avg_confidence', 0):.4f}

## Trends
- PROPOSE Reward Trend: {summary.get('propose_reward_trend', 0):.4f}
- SOLVE Reward Trend: {summary.get('solve_reward_trend', 0):.4f}
- Accuracy Trend: {summary.get('accuracy_trend', 0):.4f}

## Task Statistics
{json.dumps(final_stats.get('reward_statistics', {}), indent=2)}
"""
    
    return report


class ProgressTracker:
    """Track training progress and metrics."""
    
    def __init__(self):
        self.metrics = {
            'iterations': [],
            'propose_rewards': [],
            'solve_rewards': [],
            'accuracies': [],
            'task_counts': [],
            'solution_counts': []
        }
    
    def update(self, iteration: int, phase: str, results: Dict[str, Any]):
        """Update metrics with new results."""
        self.metrics['iterations'].append(iteration)
        
        if phase == 'PROPOSE':
            self.metrics['propose_rewards'].append(results['avg_reward'])
            self.metrics['task_counts'].append(results.get('valid_tasks', 0))
        elif phase == 'SOLVE':
            self.metrics['solve_rewards'].append(results['avg_reward'])
            self.metrics['accuracies'].append(results.get('accuracy', 0))
            self.metrics['solution_counts'].append(results.get('correct_solutions', 0))
    
    def get_latest_metrics(self, window_size: int = 10) -> Dict[str, float]:
        """Get latest metrics averaged over window."""
        latest = {}
        
        for key, values in self.metrics.items():
            if values and key != 'iterations':
                recent_values = values[-window_size:]
                latest[f'recent_{key}'] = np.mean(recent_values)
        
        return latest
    
    def plot_progress(self, save_path: Optional[str] = None):
        """Plot training progress (requires matplotlib)."""
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))
            
            # Plot rewards
            if self.metrics['propose_rewards']:
                axes[0, 0].plot(self.metrics['propose_rewards'], label='PROPOSE Rewards')
            if self.metrics['solve_rewards']:
                axes[0, 0].plot(self.metrics['solve_rewards'], label='SOLVE Rewards')
            axes[0, 0].set_title('Reward Progress')
            axes[0, 0].legend()
            
            # Plot accuracy
            if self.metrics['accuracies']:
                axes[0, 1].plot(self.metrics['accuracies'])
                axes[0, 1].set_title('Accuracy Progress')
            
            # Plot task counts
            if self.metrics['task_counts']:
                axes[1, 0].plot(self.metrics['task_counts'])
                axes[1, 0].set_title('Valid Tasks Generated')
            
            # Plot solution counts
            if self.metrics['solution_counts']:
                axes[1, 1].plot(self.metrics['solution_counts'])
                axes[1, 1].set_title('Correct Solutions')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
                logger.info(f"Progress plot saved to {save_path}")
            else:
                plt.show()
                
        except ImportError:
            logger.warning("matplotlib not available for plotting")


# Example usage
if __name__ == "__main__":
    # Test utility functions
    config = load_config('config/config.yaml')
    print("Config loaded successfully")
    
    # Test progress tracker
    tracker = ProgressTracker()
    
    # Simulate some training data
    for i in range(10):
        if i % 2 == 0:
            tracker.update(i, 'PROPOSE', {'avg_reward': 0.5 + i * 0.05, 'valid_tasks': 3 + i})
        else:
            tracker.update(i, 'SOLVE', {'avg_reward': 0.4 + i * 0.06, 'accuracy': 0.3 + i * 0.07, 'correct_solutions': 2 + i})
    
    latest = tracker.get_latest_metrics()
    print("Latest metrics:", latest)
