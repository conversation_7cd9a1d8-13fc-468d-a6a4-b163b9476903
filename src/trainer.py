"""
Main Trainer for Absolute Zero Reasoner
Implements the self-play training loop with reinforcement learning.
"""

import os
import json
import logging
import random
from typing import Dict, Any, List, Optional
from dataclasses import asdict
import torch
import numpy as np
from tqdm import tqdm
import wandb

from .model_interface import ModelInterface, create_model_interface
from .python_executor import PythonExecutor
from .task_generator import TaskGenerator, ReasoningTask
from .task_solver import TaskSolver, SolutionAttempt
from .reward_system import RewardSystem, RewardComponents

logger = logging.getLogger(__name__)


class AbsoluteZeroTrainer:
    """Main trainer implementing the Absolute Zero self-play algorithm."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize components
        self.model = create_model_interface(
            config['model']['name'],
            device=config['model'].get('device', 'auto')
        )
        
        self.executor = PythonExecutor(
            timeout=config['self_play'].get('validation_timeout', 10)
        )
        
        self.task_generator = TaskGenerator(self.model, self.executor)
        self.task_solver = TaskSolver(self.model, self.executor)
        self.reward_system = RewardSystem(config['rewards'])
        
        # Training state
        self.iteration = 0
        self.training_history = []
        self.best_performance = 0.0
        
        # Setup logging and tracking
        self._setup_logging()
        self._setup_wandb()
        
        # Create output directories
        os.makedirs(config['paths']['output_dir'], exist_ok=True)
        os.makedirs(config['paths']['model_save_dir'], exist_ok=True)
        os.makedirs(config['paths']['logs_dir'], exist_ok=True)
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config['logging']['log_level'])
        log_file = os.path.join(self.config['paths']['logs_dir'], self.config['logging']['log_file'])
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def _setup_wandb(self):
        """Setup Weights & Biases tracking."""
        if self.config['logging'].get('wandb_project'):
            wandb.init(
                project=self.config['logging']['wandb_project'],
                config=self.config,
                name=f"azr-run-{self.iteration}"
            )
    
    def train(self) -> Dict[str, Any]:
        """Main training loop implementing the Absolute Zero algorithm."""
        
        logger.info("Starting Absolute Zero training...")
        logger.info(f"Configuration: {self.config}")
        
        max_iterations = self.config['training']['max_iterations']
        batch_size = self.config['training']['batch_size']
        
        for iteration in tqdm(range(max_iterations), desc="Training Iterations"):
            self.iteration = iteration
            
            logger.info(f"=== Iteration {iteration + 1}/{max_iterations} ===")
            
            # Determine phase (PROPOSE vs SOLVE)
            propose_ratio = self.config['self_play']['propose_solve_ratio']
            is_propose_phase = random.random() < propose_ratio
            
            if is_propose_phase:
                # PROPOSE Phase: Generate new tasks
                iteration_results = self._propose_phase(batch_size)
                phase_name = "PROPOSE"
            else:
                # SOLVE Phase: Solve existing/generated tasks
                iteration_results = self._solve_phase(batch_size)
                phase_name = "SOLVE"
            
            # Log iteration results
            self._log_iteration_results(iteration, phase_name, iteration_results)
            
            # Update model (simplified RL update)
            if iteration_results['rewards']:
                self._update_model(iteration_results['rewards'])
            
            # Save checkpoint periodically
            if (iteration + 1) % self.config['logging']['save_interval'] == 0:
                self._save_checkpoint(iteration)
            
            # Track training history
            self.training_history.append({
                'iteration': iteration,
                'phase': phase_name,
                'results': iteration_results
            })
        
        # Final evaluation and cleanup
        final_stats = self._final_evaluation()
        self._cleanup()
        
        return final_stats
    
    def _propose_phase(self, batch_size: int) -> Dict[str, Any]:
        """Execute PROPOSE phase - generate reasoning tasks."""
        
        logger.info(f"PROPOSE Phase: Generating {batch_size} tasks")
        
        # Generate tasks
        task_types = ['abduction', 'deduction', 'induction']
        generated_tasks = self.task_generator.generate_batch(batch_size, task_types)
        
        # Validate and calculate rewards
        task_rewards = []
        valid_tasks = []
        
        for task in generated_tasks:
            # Validate task
            validation_result = self.task_generator.validate_task(task)
            
            # Calculate reward
            reward_components = self.reward_system.calculate_task_reward(task, validation_result)
            task_reward = reward_components.total_task_reward(self.reward_system.task_weights)
            
            task_rewards.append(task_reward)
            
            if validation_result['is_valid']:
                valid_tasks.append(task)
            
            logger.debug(f"Generated task (reward: {task_reward:.3f}): {task.problem_statement[:100]}...")
        
        return {
            'phase': 'PROPOSE',
            'generated_tasks': len(generated_tasks),
            'valid_tasks': len(valid_tasks),
            'rewards': task_rewards,
            'avg_reward': np.mean(task_rewards) if task_rewards else 0.0,
            'tasks': valid_tasks
        }
    
    def _solve_phase(self, batch_size: int) -> Dict[str, Any]:
        """Execute SOLVE phase - solve reasoning tasks."""
        
        logger.info(f"SOLVE Phase: Solving {batch_size} tasks")
        
        # Get tasks to solve (from recent generation or create new ones)
        if len(self.reward_system.task_history) >= batch_size:
            # Use recently generated tasks
            tasks_to_solve = random.sample(self.reward_system.task_history[-20:], 
                                         min(batch_size, len(self.reward_system.task_history)))
        else:
            # Generate new tasks for solving
            tasks_to_solve = self.task_generator.generate_batch(batch_size)
        
        # Solve tasks
        solution_attempts = self.task_solver.solve_batch(tasks_to_solve)
        
        # Calculate rewards
        solution_rewards = []
        correct_solutions = 0
        
        for solution in solution_attempts:
            # Calculate reward
            reward_components = self.reward_system.calculate_solution_reward(solution)
            solution_reward = reward_components.total_solution_reward(self.reward_system.solution_weights)
            
            solution_rewards.append(solution_reward)
            
            if solution.is_correct:
                correct_solutions += 1
            
            logger.debug(f"Solution attempt (reward: {solution_reward:.3f}, correct: {solution.is_correct})")
        
        accuracy = correct_solutions / len(solution_attempts) if solution_attempts else 0.0
        
        return {
            'phase': 'SOLVE',
            'attempted_solutions': len(solution_attempts),
            'correct_solutions': correct_solutions,
            'accuracy': accuracy,
            'rewards': solution_rewards,
            'avg_reward': np.mean(solution_rewards) if solution_rewards else 0.0,
            'solutions': solution_attempts
        }
    
    def _update_model(self, rewards: List[float]):
        """Update model using simplified reinforcement learning."""
        
        # This is a simplified RL update - in practice, you'd implement
        # more sophisticated algorithms like PPO, REINFORCE, etc.
        
        avg_reward = np.mean(rewards)
        
        # Simple reward-based learning signal
        # In a full implementation, you would:
        # 1. Calculate policy gradients
        # 2. Update model parameters
        # 3. Apply gradient clipping
        # 4. Update learning rate schedule
        
        logger.info(f"Model update - Average reward: {avg_reward:.4f}")
        
        # For now, just log the learning signal
        # In practice, this would involve actual parameter updates
        learning_signal = {
            'avg_reward': avg_reward,
            'reward_std': np.std(rewards),
            'max_reward': np.max(rewards),
            'min_reward': np.min(rewards)
        }
        
        return learning_signal
    
    def _log_iteration_results(self, iteration: int, phase: str, results: Dict[str, Any]):
        """Log results from an iteration."""
        
        logger.info(f"Iteration {iteration + 1} ({phase}) Results:")
        logger.info(f"  Average Reward: {results['avg_reward']:.4f}")
        
        if phase == "PROPOSE":
            logger.info(f"  Generated Tasks: {results['generated_tasks']}")
            logger.info(f"  Valid Tasks: {results['valid_tasks']}")
        else:  # SOLVE
            logger.info(f"  Attempted Solutions: {results['attempted_solutions']}")
            logger.info(f"  Correct Solutions: {results['correct_solutions']}")
            logger.info(f"  Accuracy: {results['accuracy']:.4f}")
        
        # Log to wandb if available
        if wandb.run:
            wandb.log({
                'iteration': iteration,
                'phase': phase,
                f'{phase.lower()}_avg_reward': results['avg_reward'],
                **{f'{phase.lower()}_{k}': v for k, v in results.items() 
                   if isinstance(v, (int, float))}
            })
    
    def _save_checkpoint(self, iteration: int):
        """Save training checkpoint."""
        
        checkpoint_path = os.path.join(
            self.config['paths']['model_save_dir'], 
            f"checkpoint_iter_{iteration + 1}.json"
        )
        
        checkpoint_data = {
            'iteration': iteration,
            'config': self.config,
            'training_history': self.training_history[-10:],  # Last 10 iterations
            'reward_stats': self.reward_system.get_statistics(),
            'best_performance': self.best_performance
        }
        
        with open(checkpoint_path, 'w') as f:
            json.dump(checkpoint_data, f, indent=2, default=str)
        
        logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def _final_evaluation(self) -> Dict[str, Any]:
        """Perform final evaluation and return training statistics."""
        
        logger.info("Performing final evaluation...")
        
        # Generate evaluation tasks
        eval_tasks = self.task_generator.generate_batch(10)
        eval_solutions = self.task_solver.solve_batch(eval_tasks)
        
        # Calculate final metrics
        final_accuracy = sum(1 for s in eval_solutions if s.is_correct) / len(eval_solutions)
        final_avg_confidence = np.mean([s.confidence for s in eval_solutions])
        
        # Get reward system statistics
        reward_stats = self.reward_system.get_statistics()
        
        final_stats = {
            'total_iterations': self.iteration + 1,
            'final_accuracy': final_accuracy,
            'final_avg_confidence': final_avg_confidence,
            'reward_statistics': reward_stats,
            'training_history_length': len(self.training_history)
        }
        
        logger.info(f"Final Statistics: {final_stats}")
        
        # Save final results
        results_path = os.path.join(self.config['paths']['output_dir'], 'final_results.json')
        with open(results_path, 'w') as f:
            json.dump(final_stats, f, indent=2, default=str)
        
        return final_stats
    
    def _cleanup(self):
        """Cleanup resources and finalize logging."""
        
        if wandb.run:
            wandb.finish()
        
        logger.info("Training completed successfully!")


# Example usage
if __name__ == "__main__":
    import yaml
    
    # Load configuration
    with open('config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create and run trainer
    trainer = AbsoluteZeroTrainer(config)
    results = trainer.train()
    
    print("Training completed!")
    print(f"Final results: {results}")
