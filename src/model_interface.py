"""
Interface for different language models (local and API-based).
Provides a unified interface for text generation.
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from typing import List, Dict, Any, Optional
import openai
import anthropic
import time
import logging

logger = logging.getLogger(__name__)


class ModelInterface:
    """Base class for model interfaces."""
    
    def generate(self, prompt: str, max_length: int = 512, **kwargs) -> str:
        """Generate text from prompt."""
        raise NotImplementedError
    
    def generate_batch(self, prompts: List[str], max_length: int = 512, **kwargs) -> List[str]:
        """Generate text for multiple prompts."""
        return [self.generate(prompt, max_length, **kwargs) for prompt in prompts]


class HuggingFaceModel(ModelInterface):
    """Interface for HuggingFace transformers models."""
    
    def __init__(self, model_name: str, device: str = "auto"):
        self.model_name = model_name
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        
        logger.info(f"Loading model {model_name} on {self.device}")
        
        # Load tokenizer and model
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )
        
        if self.device == "cpu":
            self.model = self.model.to(self.device)
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7, 
                 top_p: float = 0.9, do_sample: bool = True, **kwargs) -> str:
        """Generate text from prompt using HuggingFace model."""
        
        # Tokenize input
        inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=min(len(inputs[0]) + max_length, self.model.config.max_position_embeddings),
                temperature=temperature,
                top_p=top_p,
                do_sample=do_sample,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                **kwargs
            )
        
        # Decode and return only the generated part
        generated_text = self.tokenizer.decode(outputs[0][len(inputs[0]):], skip_special_tokens=True)
        return generated_text.strip()
    
    def generate_batch(self, prompts: List[str], max_length: int = 512, **kwargs) -> List[str]:
        """Generate text for multiple prompts efficiently."""
        # Tokenize all prompts
        inputs = self.tokenizer(prompts, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=max_length,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                **kwargs
            )
        
        # Decode results
        results = []
        for i, output in enumerate(outputs):
            # Get only the generated part (after input)
            input_length = len(inputs['input_ids'][i])
            generated_text = self.tokenizer.decode(output[input_length:], skip_special_tokens=True)
            results.append(generated_text.strip())
        
        return results


class OpenAIModel(ModelInterface):
    """Interface for OpenAI API models."""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo", api_key: Optional[str] = None):
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7, **kwargs) -> str:
        """Generate text using OpenAI API."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_length,
                temperature=temperature,
                **kwargs
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return ""


class AnthropicModel(ModelInterface):
    """Interface for Anthropic Claude models."""
    
    def __init__(self, model_name: str = "claude-3-sonnet-20240229", api_key: Optional[str] = None):
        self.model_name = model_name
        self.client = anthropic.Anthropic(api_key=api_key)
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7, **kwargs) -> str:
        """Generate text using Anthropic API."""
        try:
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=max_length,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text.strip()
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            return ""


def create_model_interface(model_name: str, **kwargs) -> ModelInterface:
    """Factory function to create appropriate model interface."""
    
    if model_name.startswith("gpt-"):
        return OpenAIModel(model_name, **kwargs)
    elif model_name.startswith("claude-"):
        return AnthropicModel(model_name, **kwargs)
    else:
        # Assume HuggingFace model
        return HuggingFaceModel(model_name, **kwargs)


# Example usage
if __name__ == "__main__":
    # Test with a small HuggingFace model
    model = create_model_interface("microsoft/DialoGPT-small")
    
    prompt = "What is the capital of France?"
    response = model.generate(prompt, max_length=50)
    print(f"Prompt: {prompt}")
    print(f"Response: {response}")
