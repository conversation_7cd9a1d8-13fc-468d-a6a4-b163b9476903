"""
Task Solver - SOLVE Phase Implementation
Attempts to solve self-generated reasoning tasks.
"""

import re
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .model_interface import ModelInterface
from .python_executor import PythonExecutor
from .task_generator import ReasoningTask

logger = logging.getLogger(__name__)


@dataclass
class SolutionAttempt:
    """Data class for a solution attempt."""
    task: ReasoningTask
    generated_solution: str
    is_correct: bool
    confidence: float
    execution_result: Optional[Dict[str, Any]] = None
    reasoning_steps: List[str] = None
    metadata: Dict[str, Any] = None


class TaskSolver:
    """Solves reasoning tasks for the SOLVE phase."""
    
    def __init__(self, model: ModelInterface, executor: PythonExecutor):
        self.model = model
        self.executor = executor
        
        # Solution prompts for different task types
        self.solve_prompts = {
            'abduction': """You are given a problem that requires finding the most likely explanation for observations.

Problem: {problem}

Think step by step and provide the most likely explanation. Format your response as:

Reasoning:
[Your step-by-step reasoning]

Answer:
[Your final answer]""",

            'deduction': """You are given a problem that requires deriving conclusions from given premises.

Problem: {problem}

Think step by step and derive the logical conclusion. Format your response as:

Reasoning:
[Your step-by-step reasoning]

Answer:
[Your final answer]""",

            'induction': """You are given a problem that requires inferring general patterns from specific examples.

Problem: {problem}

Think step by step and identify the pattern. Format your response as:

Reasoning:
[Your step-by-step reasoning]

Answer:
[Your final answer]"""
        }
    
    def solve_task(self, task: ReasoningTask) -> SolutionAttempt:
        """Attempt to solve a single reasoning task."""
        
        # Get appropriate prompt for task type
        prompt_template = self.solve_prompts.get(task.task_type, self.solve_prompts['deduction'])
        prompt = prompt_template.format(problem=task.problem_statement)
        
        # Generate solution
        generated_solution = self.model.generate(
            prompt,
            max_length=500,
            temperature=0.7,
            top_p=0.9
        )
        
        # Parse the generated solution
        reasoning_steps, final_answer = self._parse_solution(generated_solution)
        
        # Evaluate correctness
        is_correct, confidence, execution_result = self._evaluate_solution(
            task, generated_solution, final_answer
        )
        
        return SolutionAttempt(
            task=task,
            generated_solution=generated_solution,
            is_correct=is_correct,
            confidence=confidence,
            execution_result=execution_result,
            reasoning_steps=reasoning_steps,
            metadata={
                'final_answer': final_answer,
                'prompt_used': prompt
            }
        )
    
    def solve_batch(self, tasks: List[ReasoningTask]) -> List[SolutionAttempt]:
        """Solve multiple tasks in batch."""
        solutions = []
        
        for task in tasks:
            try:
                solution = self.solve_task(task)
                solutions.append(solution)
            except Exception as e:
                logger.warning(f"Failed to solve task: {e}")
                # Create a failed solution attempt
                solutions.append(SolutionAttempt(
                    task=task,
                    generated_solution="",
                    is_correct=False,
                    confidence=0.0,
                    metadata={'error': str(e)}
                ))
        
        return solutions
    
    def _parse_solution(self, generated_solution: str) -> tuple[List[str], str]:
        """Parse generated solution to extract reasoning steps and final answer."""
        
        # Look for Reasoning: and Answer: sections
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?=Answer:|$)', generated_solution, re.DOTALL | re.IGNORECASE)
        answer_match = re.search(r'Answer:\s*(.*?)$', generated_solution, re.DOTALL | re.IGNORECASE)
        
        reasoning_steps = []
        final_answer = ""
        
        if reasoning_match:
            reasoning_text = reasoning_match.group(1).strip()
            # Split reasoning into steps (by lines or numbered points)
            reasoning_steps = [step.strip() for step in reasoning_text.split('\n') if step.strip()]
        
        if answer_match:
            final_answer = answer_match.group(1).strip()
        else:
            # Fallback: use last non-empty line as answer
            lines = [line.strip() for line in generated_solution.split('\n') if line.strip()]
            if lines:
                final_answer = lines[-1]
        
        return reasoning_steps, final_answer
    
    def _evaluate_solution(self, task: ReasoningTask, generated_solution: str, 
                          final_answer: str) -> tuple[bool, float, Optional[Dict[str, Any]]]:
        """Evaluate the correctness of a solution."""
        
        execution_result = None
        is_correct = False
        confidence = 0.0
        
        # For code-related tasks, try to execute and compare
        if task.domain == 'code':
            is_correct, confidence, execution_result = self._evaluate_code_solution(
                task, generated_solution, final_answer
            )
        
        # For math tasks, try to validate numerically
        elif task.domain == 'math':
            is_correct, confidence = self._evaluate_math_solution(
                task, generated_solution, final_answer
            )
        
        # For logic tasks, use heuristic evaluation
        else:
            is_correct, confidence = self._evaluate_logic_solution(
                task, generated_solution, final_answer
            )
        
        return is_correct, confidence, execution_result
    
    def _evaluate_code_solution(self, task: ReasoningTask, generated_solution: str, 
                               final_answer: str) -> tuple[bool, float, Dict[str, Any]]:
        """Evaluate code-related solutions by execution."""
        
        # Extract code from the solution
        code_blocks = re.findall(r'```python\n(.*?)\n```', generated_solution, re.DOTALL)
        if not code_blocks:
            # Look for code without markdown
            code_blocks = re.findall(r'def\s+\w+.*?(?=\n\n|\n[A-Z]|$)', generated_solution, re.DOTALL)
        
        if not code_blocks:
            return False, 0.1, {'error': 'No executable code found'}
        
        # Execute the code
        execution_result = self.executor.execute_code(code_blocks[0])
        
        if not execution_result['success']:
            return False, 0.2, execution_result
        
        # Compare with expected result if available
        # This is a simplified comparison - in practice, you'd want more sophisticated matching
        confidence = 0.7 if execution_result['success'] else 0.2
        is_correct = execution_result['success'] and not execution_result['error']
        
        return is_correct, confidence, execution_result
    
    def _evaluate_math_solution(self, task: ReasoningTask, generated_solution: str, 
                               final_answer: str) -> tuple[bool, float]:
        """Evaluate mathematical solutions."""
        
        # Extract numerical answers
        numbers_in_answer = re.findall(r'-?\d+\.?\d*', final_answer)
        numbers_in_expected = re.findall(r'-?\d+\.?\d*', task.solution)
        
        # Simple heuristic: if we find matching numbers, assume correct
        if numbers_in_answer and numbers_in_expected:
            # Check if any numbers match (allowing for small floating point differences)
            for ans_num in numbers_in_answer:
                for exp_num in numbers_in_expected:
                    try:
                        if abs(float(ans_num) - float(exp_num)) < 0.001:
                            return True, 0.8
                    except ValueError:
                        continue
        
        # Check for keyword matches
        answer_words = set(final_answer.lower().split())
        expected_words = set(task.solution.lower().split())
        overlap = len(answer_words.intersection(expected_words))
        
        if overlap > 3:
            return True, 0.6
        elif overlap > 1:
            return False, 0.4
        else:
            return False, 0.2
    
    def _evaluate_logic_solution(self, task: ReasoningTask, generated_solution: str, 
                                final_answer: str) -> tuple[bool, float]:
        """Evaluate logical reasoning solutions."""
        
        # For logic tasks, use semantic similarity and keyword matching
        answer_words = set(final_answer.lower().split())
        expected_words = set(task.solution.lower().split())
        
        # Calculate Jaccard similarity
        intersection = len(answer_words.intersection(expected_words))
        union = len(answer_words.union(expected_words))
        
        if union == 0:
            similarity = 0.0
        else:
            similarity = intersection / union
        
        # Consider correct if similarity is high enough
        is_correct = similarity > 0.3
        confidence = min(similarity * 2, 1.0)  # Scale to [0, 1]
        
        return is_correct, confidence
    
    def get_solution_quality_metrics(self, solution: SolutionAttempt) -> Dict[str, float]:
        """Calculate quality metrics for a solution attempt."""
        
        metrics = {
            'correctness': 1.0 if solution.is_correct else 0.0,
            'confidence': solution.confidence,
            'reasoning_length': len(solution.reasoning_steps) if solution.reasoning_steps else 0,
            'solution_length': len(solution.generated_solution),
        }
        
        # Add execution-based metrics if available
        if solution.execution_result:
            metrics['executable'] = 1.0 if solution.execution_result['success'] else 0.0
            metrics['has_output'] = 1.0 if solution.execution_result['output'] else 0.0
        
        # Calculate overall quality score
        metrics['quality_score'] = (
            metrics['correctness'] * 0.5 +
            metrics['confidence'] * 0.3 +
            min(metrics['reasoning_length'] / 5, 1.0) * 0.2
        )
        
        return metrics


# Example usage and testing
if __name__ == "__main__":
    from .model_interface import create_model_interface
    from .task_generator import TaskGenerator
    
    # Create components
    model = create_model_interface("microsoft/DialoGPT-small")
    executor = PythonExecutor()
    generator = TaskGenerator(model, executor)
    solver = TaskSolver(model, executor)
    
    # Generate a sample task
    print("Generating sample task...")
    task = generator.generate_task('deduction', 'math')
    print(f"Task: {task.problem_statement}")
    print(f"Expected: {task.solution}")
    
    # Solve the task
    print("\nSolving task...")
    solution = solver.solve_task(task)
    print(f"Generated Solution: {solution.generated_solution}")
    print(f"Correct: {solution.is_correct}")
    print(f"Confidence: {solution.confidence}")
    
    # Get quality metrics
    metrics = solver.get_solution_quality_metrics(solution)
    print(f"Quality Metrics: {metrics}")
