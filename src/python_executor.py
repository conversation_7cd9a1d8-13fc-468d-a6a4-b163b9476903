"""
Safe Python execution environment for validating tasks and solutions.
Implements security measures and timeout protection.
"""

import ast
import sys
import io
import signal
import traceback
from contextlib import contextmanager
from typing import Dict, Any, Optional, Tuple
import re
import math


class TimeoutError(Exception):
    """Custom timeout exception."""
    pass


class PythonExecutor:
    """Safe Python code executor with timeout and security restrictions."""
    
    def __init__(self, timeout: int = 10):
        self.timeout = timeout
        self.allowed_modules = {
            'math', 'random', 'itertools', 'collections', 
            'functools', 're', 'string', 'datetime'
        }
        self.forbidden_patterns = [
            r'import\s+os', r'import\s+subprocess', r'import\s+sys',
            r'__import__', r'eval\s*\(', r'exec\s*\(',
            r'open\s*\(', r'file\s*\(', r'input\s*\(',
            r'raw_input\s*\(', r'compile\s*\('
        ]
    
    @contextmanager
    def timeout_handler(self):
        """Context manager for handling execution timeout."""
        def timeout_signal_handler(signum, frame):
            raise TimeoutError("Code execution timed out")
        
        # Set up signal handler
        old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
        signal.alarm(self.timeout)
        
        try:
            yield
        finally:
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)
    
    def is_safe_code(self, code: str) -> Tuple[bool, str]:
        """Check if code is safe to execute."""
        # Check for forbidden patterns
        for pattern in self.forbidden_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                return False, f"Forbidden pattern detected: {pattern}"
        
        # Parse AST to check for dangerous operations
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name not in self.allowed_modules:
                            return False, f"Forbidden import: {alias.name}"
                elif isinstance(node, ast.ImportFrom):
                    if node.module not in self.allowed_modules:
                        return False, f"Forbidden import from: {node.module}"
        except SyntaxError as e:
            return False, f"Syntax error: {str(e)}"
        
        return True, "Code is safe"
    
    def execute_code(self, code: str) -> Dict[str, Any]:
        """
        Execute Python code safely and return results.
        
        Args:
            code: Python code string to execute
            
        Returns:
            Dictionary with execution results:
            - success: bool indicating if execution succeeded
            - output: captured stdout
            - error: error message if any
            - result: final expression result if any
            - variables: final variable state
        """
        # Check code safety
        is_safe, safety_msg = self.is_safe_code(code)
        if not is_safe:
            return {
                'success': False,
                'output': '',
                'error': f"Security violation: {safety_msg}",
                'result': None,
                'variables': {}
            }
        
        # Prepare execution environment
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        # Create restricted globals
        safe_globals = {
            '__builtins__': {
                'abs': abs, 'all': all, 'any': any, 'bin': bin, 'bool': bool,
                'chr': chr, 'dict': dict, 'divmod': divmod, 'enumerate': enumerate,
                'filter': filter, 'float': float, 'format': format, 'frozenset': frozenset,
                'hex': hex, 'int': int, 'len': len, 'list': list, 'map': map,
                'max': max, 'min': min, 'oct': oct, 'ord': ord, 'pow': pow,
                'print': print, 'range': range, 'reversed': reversed, 'round': round,
                'set': set, 'slice': slice, 'sorted': sorted, 'str': str,
                'sum': sum, 'tuple': tuple, 'type': type, 'zip': zip,
            },
            'math': math,
        }
        
        local_vars = {}
        
        try:
            with self.timeout_handler():
                # Redirect stdout/stderr
                old_stdout = sys.stdout
                old_stderr = sys.stderr
                sys.stdout = stdout_capture
                sys.stderr = stderr_capture
                
                try:
                    # Execute the code
                    exec(code, safe_globals, local_vars)
                    
                    # Try to get the result of the last expression
                    result = None
                    try:
                        # Parse and check if last statement is an expression
                        tree = ast.parse(code)
                        if tree.body and isinstance(tree.body[-1], ast.Expr):
                            last_expr = ast.Expression(tree.body[-1].value)
                            result = eval(compile(last_expr, '<string>', 'eval'), 
                                        safe_globals, local_vars)
                    except:
                        pass
                    
                    return {
                        'success': True,
                        'output': stdout_capture.getvalue(),
                        'error': stderr_capture.getvalue(),
                        'result': result,
                        'variables': {k: v for k, v in local_vars.items() 
                                    if not k.startswith('_')}
                    }
                    
                finally:
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr
                    
        except TimeoutError:
            return {
                'success': False,
                'output': stdout_capture.getvalue(),
                'error': 'Execution timed out',
                'result': None,
                'variables': {}
            }
        except Exception as e:
            return {
                'success': False,
                'output': stdout_capture.getvalue(),
                'error': f"{type(e).__name__}: {str(e)}",
                'result': None,
                'variables': {}
            }
    
    def validate_task_solution(self, task: str, solution: str) -> Dict[str, Any]:
        """
        Validate a task-solution pair by executing the solution.
        
        Args:
            task: The problem statement
            solution: The proposed solution code
            
        Returns:
            Validation results including correctness and execution details
        """
        execution_result = self.execute_code(solution)
        
        # Basic validation - solution should execute without errors
        is_valid = execution_result['success'] and not execution_result['error']
        
        return {
            'is_valid': is_valid,
            'execution_result': execution_result,
            'task': task,
            'solution': solution
        }


# Example usage and testing
if __name__ == "__main__":
    executor = PythonExecutor(timeout=5)
    
    # Test safe code
    safe_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(10)
print(f"Fibonacci(10) = {result}")
result
"""
    
    print("Testing safe code:")
    result = executor.execute_code(safe_code)
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")
    print(f"Result: {result['result']}")
    
    # Test unsafe code
    unsafe_code = "import os; os.system('ls')"
    print("\nTesting unsafe code:")
    result = executor.execute_code(unsafe_code)
    print(f"Success: {result['success']}")
    print(f"Error: {result['error']}")
