"""
Reward System for Absolute Zero Reasoner
Calculates intrinsic rewards for task generation and solution quality.
"""

import math
import numpy as np
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
import logging

from .task_generator import ReasoningTask
from .task_solver import SolutionAttempt

logger = logging.getLogger(__name__)


@dataclass
class RewardComponents:
    """Individual reward components."""
    task_complexity: float = 0.0
    task_clarity: float = 0.0
    task_solvability: float = 0.0
    task_diversity: float = 0.0
    solution_accuracy: float = 0.0
    solution_efficiency: float = 0.0
    solution_elegance: float = 0.0
    
    def total_task_reward(self, weights: Dict[str, float]) -> float:
        """Calculate total task generation reward."""
        return (
            self.task_complexity * weights.get('task_complexity_weight', 0.3) +
            self.task_clarity * weights.get('task_clarity_weight', 0.2) +
            self.task_solvability * weights.get('task_solvability_weight', 0.3) +
            self.task_diversity * weights.get('task_diversity_weight', 0.2)
        )
    
    def total_solution_reward(self, weights: Dict[str, float]) -> float:
        """Calculate total solution reward."""
        return (
            self.solution_accuracy * weights.get('accuracy_weight', 0.6) +
            self.solution_efficiency * weights.get('efficiency_weight', 0.2) +
            self.solution_elegance * weights.get('elegance_weight', 0.2)
        )


class RewardSystem:
    """Calculates intrinsic rewards for the Absolute Zero training process."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.task_history = []  # For diversity calculation
        self.solution_history = []  # For tracking improvement
        
        # Reward weights from config
        self.task_weights = {
            'task_complexity_weight': config.get('task_complexity_weight', 0.3),
            'task_clarity_weight': config.get('task_clarity_weight', 0.2),
            'task_solvability_weight': config.get('task_solvability_weight', 0.3),
            'task_diversity_weight': config.get('task_diversity_weight', 0.2),
        }
        
        self.solution_weights = {
            'accuracy_weight': config.get('accuracy_weight', 0.6),
            'efficiency_weight': config.get('efficiency_weight', 0.2),
            'elegance_weight': config.get('elegance_weight', 0.2),
        }
    
    def calculate_task_reward(self, task: ReasoningTask, validation_result: Dict[str, Any]) -> RewardComponents:
        """Calculate reward for a generated task (PROPOSE phase)."""
        
        rewards = RewardComponents()
        
        # Task Complexity Reward
        rewards.task_complexity = self._calculate_complexity_reward(task)
        
        # Task Clarity Reward
        rewards.task_clarity = self._calculate_clarity_reward(task)
        
        # Task Solvability Reward
        rewards.task_solvability = self._calculate_solvability_reward(task, validation_result)
        
        # Task Diversity Reward
        rewards.task_diversity = self._calculate_diversity_reward(task)
        
        # Add to history for future diversity calculations
        self.task_history.append(task)
        
        return rewards
    
    def calculate_solution_reward(self, solution: SolutionAttempt) -> RewardComponents:
        """Calculate reward for a solution attempt (SOLVE phase)."""
        
        rewards = RewardComponents()
        
        # Solution Accuracy Reward
        rewards.solution_accuracy = self._calculate_accuracy_reward(solution)
        
        # Solution Efficiency Reward
        rewards.solution_efficiency = self._calculate_efficiency_reward(solution)
        
        # Solution Elegance Reward
        rewards.solution_elegance = self._calculate_elegance_reward(solution)
        
        # Add to history
        self.solution_history.append(solution)
        
        return rewards
    
    def _calculate_complexity_reward(self, task: ReasoningTask) -> float:
        """Calculate reward based on task complexity."""
        
        # Factors that contribute to complexity
        complexity_score = 0.0
        
        # Length-based complexity
        problem_length = len(task.problem_statement)
        solution_length = len(task.solution)
        length_score = min((problem_length + solution_length) / 500, 1.0)
        complexity_score += length_score * 0.3
        
        # Keyword-based complexity
        complex_keywords = [
            'equation', 'derivative', 'integral', 'matrix', 'algorithm',
            'recursive', 'optimization', 'probability', 'statistics',
            'polynomial', 'logarithm', 'exponential', 'trigonometric'
        ]
        
        text = (task.problem_statement + " " + task.solution).lower()
        keyword_count = sum(1 for keyword in complex_keywords if keyword in text)
        keyword_score = min(keyword_count / 5, 1.0)
        complexity_score += keyword_score * 0.4
        
        # Difficulty-based complexity
        difficulty_scores = {'easy': 0.3, 'medium': 0.6, 'hard': 1.0}
        difficulty_score = difficulty_scores.get(task.difficulty, 0.5)
        complexity_score += difficulty_score * 0.3
        
        return min(complexity_score, 1.0)
    
    def _calculate_clarity_reward(self, task: ReasoningTask) -> float:
        """Calculate reward based on task clarity and well-formedness."""
        
        clarity_score = 0.0
        
        # Check for clear problem statement
        if len(task.problem_statement.strip()) > 20:
            clarity_score += 0.3
        
        # Check for clear solution
        if len(task.solution.strip()) > 20:
            clarity_score += 0.3
        
        # Check for structured format (keywords like "Problem:", "Solution:")
        structure_keywords = ['problem', 'solution', 'answer', 'find', 'calculate', 'determine']
        text = task.problem_statement.lower()
        if any(keyword in text for keyword in structure_keywords):
            clarity_score += 0.2
        
        # Penalize very short or very long tasks
        total_length = len(task.problem_statement) + len(task.solution)
        if 50 <= total_length <= 1000:
            clarity_score += 0.2
        
        return min(clarity_score, 1.0)
    
    def _calculate_solvability_reward(self, task: ReasoningTask, validation_result: Dict[str, Any]) -> float:
        """Calculate reward based on task solvability."""
        
        solvability_score = 0.0
        
        # Basic validation passed
        if validation_result.get('is_valid', False):
            solvability_score += 0.5
        
        # Code execution successful (for code tasks)
        if validation_result.get('executable', False):
            execution_result = validation_result.get('execution_result', {})
            if execution_result.get('success', False):
                solvability_score += 0.3
            
            # Has meaningful output
            if execution_result.get('output') or execution_result.get('result') is not None:
                solvability_score += 0.2
        
        # No validation issues
        if not validation_result.get('issues', []):
            solvability_score += 0.2
        else:
            # Penalize based on number of issues
            penalty = min(len(validation_result['issues']) * 0.1, 0.3)
            solvability_score -= penalty
        
        return max(min(solvability_score, 1.0), 0.0)
    
    def _calculate_diversity_reward(self, task: ReasoningTask) -> float:
        """Calculate reward based on task diversity compared to history."""
        
        if len(self.task_history) < 2:
            return 1.0  # Maximum diversity for first few tasks
        
        # Compare with recent tasks (last 10)
        recent_tasks = self.task_history[-10:]
        
        diversity_score = 1.0
        
        # Check task type diversity
        recent_types = [t.task_type for t in recent_tasks]
        type_frequency = recent_types.count(task.task_type) / len(recent_types)
        diversity_score *= (1.0 - type_frequency * 0.5)
        
        # Check domain diversity
        recent_domains = [t.domain for t in recent_tasks]
        domain_frequency = recent_domains.count(task.domain) / len(recent_domains)
        diversity_score *= (1.0 - domain_frequency * 0.3)
        
        # Check content similarity (simple keyword overlap)
        current_words = set(task.problem_statement.lower().split())
        
        max_similarity = 0.0
        for past_task in recent_tasks:
            past_words = set(past_task.problem_statement.lower().split())
            if len(current_words.union(past_words)) > 0:
                similarity = len(current_words.intersection(past_words)) / len(current_words.union(past_words))
                max_similarity = max(max_similarity, similarity)
        
        diversity_score *= (1.0 - max_similarity * 0.4)
        
        return max(min(diversity_score, 1.0), 0.1)
    
    def _calculate_accuracy_reward(self, solution: SolutionAttempt) -> float:
        """Calculate reward based on solution accuracy."""
        
        if solution.is_correct:
            # Base accuracy reward
            accuracy_score = 0.8
            
            # Bonus for high confidence
            accuracy_score += solution.confidence * 0.2
            
            return min(accuracy_score, 1.0)
        else:
            # Partial credit based on confidence and effort
            partial_score = solution.confidence * 0.3
            
            # Small bonus for attempting reasoning
            if solution.reasoning_steps and len(solution.reasoning_steps) > 0:
                partial_score += 0.1
            
            return min(partial_score, 0.4)
    
    def _calculate_efficiency_reward(self, solution: SolutionAttempt) -> float:
        """Calculate reward based on solution efficiency."""
        
        efficiency_score = 0.5  # Base score
        
        # Reward concise solutions
        solution_length = len(solution.generated_solution)
        if solution_length < 200:
            efficiency_score += 0.3
        elif solution_length < 500:
            efficiency_score += 0.1
        
        # Reward fast execution (if applicable)
        if solution.execution_result:
            # Assume faster execution is better (this is simplified)
            efficiency_score += 0.2
        
        # Reward clear reasoning structure
        if solution.reasoning_steps:
            step_count = len(solution.reasoning_steps)
            if 2 <= step_count <= 5:  # Optimal number of steps
                efficiency_score += 0.2
        
        return min(efficiency_score, 1.0)
    
    def _calculate_elegance_reward(self, solution: SolutionAttempt) -> float:
        """Calculate reward based on solution elegance and style."""
        
        elegance_score = 0.5  # Base score
        
        # Reward well-structured reasoning
        if solution.reasoning_steps:
            # Check for logical flow indicators
            flow_keywords = ['first', 'then', 'next', 'therefore', 'thus', 'finally']
            reasoning_text = ' '.join(solution.reasoning_steps).lower()
            flow_count = sum(1 for keyword in flow_keywords if keyword in reasoning_text)
            elegance_score += min(flow_count * 0.1, 0.3)
        
        # Reward clear final answer
        if solution.metadata and solution.metadata.get('final_answer'):
            final_answer = solution.metadata['final_answer']
            if len(final_answer.strip()) > 5:
                elegance_score += 0.2
        
        # Penalize overly verbose solutions
        if len(solution.generated_solution) > 800:
            elegance_score -= 0.2
        
        return max(min(elegance_score, 1.0), 0.0)
    
    def get_training_signal(self, task_rewards: RewardComponents, 
                           solution_rewards: RewardComponents) -> Dict[str, float]:
        """Combine rewards into training signals for RL."""
        
        # Calculate total rewards
        total_task_reward = task_rewards.total_task_reward(self.task_weights)
        total_solution_reward = solution_rewards.total_solution_reward(self.solution_weights)
        
        # Combine into overall training signal
        # In practice, you might want to weight these differently or use them separately
        combined_reward = (total_task_reward + total_solution_reward) / 2
        
        return {
            'task_reward': total_task_reward,
            'solution_reward': total_solution_reward,
            'combined_reward': combined_reward,
            'individual_components': {
                'task_complexity': task_rewards.task_complexity,
                'task_clarity': task_rewards.task_clarity,
                'task_solvability': task_rewards.task_solvability,
                'task_diversity': task_rewards.task_diversity,
                'solution_accuracy': solution_rewards.solution_accuracy,
                'solution_efficiency': solution_rewards.solution_efficiency,
                'solution_elegance': solution_rewards.solution_elegance,
            }
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about reward distribution and trends."""
        
        if not self.task_history or not self.solution_history:
            return {'message': 'No data available yet'}
        
        # Calculate recent performance trends
        recent_solutions = self.solution_history[-10:] if len(self.solution_history) >= 10 else self.solution_history
        
        accuracy_trend = [s.is_correct for s in recent_solutions]
        confidence_trend = [s.confidence for s in recent_solutions]
        
        stats = {
            'total_tasks_generated': len(self.task_history),
            'total_solutions_attempted': len(self.solution_history),
            'recent_accuracy_rate': sum(accuracy_trend) / len(accuracy_trend) if accuracy_trend else 0,
            'recent_avg_confidence': sum(confidence_trend) / len(confidence_trend) if confidence_trend else 0,
            'task_type_distribution': {},
            'domain_distribution': {},
        }
        
        # Task type distribution
        for task in self.task_history:
            stats['task_type_distribution'][task.task_type] = stats['task_type_distribution'].get(task.task_type, 0) + 1
        
        # Domain distribution
        for task in self.task_history:
            stats['domain_distribution'][task.domain] = stats['domain_distribution'].get(task.domain, 0) + 1
        
        return stats


# Example usage
if __name__ == "__main__":
    # Test reward system
    config = {
        'task_complexity_weight': 0.3,
        'task_clarity_weight': 0.2,
        'task_solvability_weight': 0.3,
        'task_diversity_weight': 0.2,
        'accuracy_weight': 0.6,
        'efficiency_weight': 0.2,
        'elegance_weight': 0.2,
    }
    
    reward_system = RewardSystem(config)
    
    # Create sample task and solution for testing
    from .task_generator import ReasoningTask
    from .task_solver import SolutionAttempt
    
    sample_task = ReasoningTask(
        task_type='deduction',
        problem_statement='Find the value of x if 2x + 5 = 15',
        solution='2x = 15 - 5 = 10, so x = 5',
        difficulty='easy',
        domain='math'
    )
    
    sample_solution = SolutionAttempt(
        task=sample_task,
        generated_solution='Reasoning: 2x + 5 = 15, subtract 5 from both sides: 2x = 10, divide by 2: x = 5. Answer: x = 5',
        is_correct=True,
        confidence=0.9,
        reasoning_steps=['2x + 5 = 15', 'subtract 5: 2x = 10', 'divide by 2: x = 5']
    )
    
    # Calculate rewards
    validation_result = {'is_valid': True, 'issues': []}
    task_rewards = reward_system.calculate_task_reward(sample_task, validation_result)
    solution_rewards = reward_system.calculate_solution_reward(sample_solution)
    
    training_signal = reward_system.get_training_signal(task_rewards, solution_rewards)
    
    print("Task Rewards:", task_rewards)
    print("Solution Rewards:", solution_rewards)
    print("Training Signal:", training_signal)
